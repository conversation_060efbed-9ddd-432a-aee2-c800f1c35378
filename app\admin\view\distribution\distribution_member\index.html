{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*分销会员；</p>
                    </div>
                </div>
            </div>
        </div>
        <!--搜索区域-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-label">用户信息：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">服务商信息：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="distribution_keyword" name="distribution_keyword" class="layui-input" />
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">账号类型：</div>
                    <div class="layui-input-inline">
                        <select name="level_id" id="level_id"  placeholder="请选择" >
                            <option value="all">全部</option>
                            {foreach $levels as $val }
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
{/*                <div class="layui-inline">*/}
{/*                    <div class="layui-form-label">销售统计：</div>*/}
{/*                    <button class="layui-btn layui-btn-primary search_date_button" data-date="today" lay-submit lay-filter="date_search">今日</button>*/}
{/*                    <button class="layui-btn layui-btn-primary search_date_button" data-date="yesterday" lay-submit lay-filter="date_search">昨日</button>*/}
{/*                    <button class="layui-btn layui-btn-primary search_date_button " data-date="this_month" lay-submit lay-filter="date_search">本月</button>*/}
{/*                    <button class="layui-btn layui-btn-primary search_date_button" data-date="last_month" lay-submit lay-filter="date_search">上月</button>*/}
{/*                    <button class="layui-btn layui-btn-primary search_date_button" data-date="all" lay-submit lay-filter="date_search">全部</button>*/}
{/*                    <input type="hidden" name="quick_date" id="quick_date" value="" />*/}
{/*                    <div class="layui-input-inline">*/}
{/*                        <div class="layui-input-inline">*/}
{/*                            <input type="text" name="start_time" class="layui-input" id="start_time"*/}
{/*                                   placeholder="" autocomplete="off">*/}
{/*                        </div>*/}
{/*                    </div>*/}
{/*                    <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">*/}
{/*                        <label class="layui-form-mid">至</label>*/}
{/*                    </div>*/}
{/*                    <div class="layui-input-inline">*/}
{/*                        <input type="text" name="end_time" class="layui-input" id="end_time"*/}
{/*                               placeholder="" autocomplete="off">*/}
{/*                    </div>*/}

{/*                </div>*/}
                <div class="layui-inline">
                    <div class="layui-form-label">账号状态：</div>
                    <div class="layui-input-inline">
                        <select name="is_freeze" id="is_freeze"  placeholder="请选择" >
                            <option value="all">全部</option>
                            <option value="0">正常</option>
                            <option value="1">冻结</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <input type="hidden" name="first_leader" value="{$first_leader}" />
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <!--功能按钮-->
            <div class="btns">
{/*                <buttion class="layui-btn layui-btn-sm layui-bg-blue" id="open">开通分销会员</buttion>*/}
            </div>
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operate">
                <a class="layui-btn layui-btn-sm layui-bg-blue" lay-event="adjust">编辑</a>
{/*                {{#  if(d.is_freeze == 0){ &cc;&cc;*/}
{/*                <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="freeze">冻结资格</a>*/}
{/*                {{#  &cc; else { &cc;&cc;*/}
{/*                <a class="layui-btn layui-bg-blue layui-btn-sm" lay-event="unfreeze">恢复资格</a>*/}
{/*                {{#  &cc; &cc;&cc;*/}
{/*                <a class="layui-btn layui-btn-normal layui-btn-sm"  id="adjust_first_leader" lay-event="adjust_first_leader">调整分佣上级</a>*/}
                <a class="layui-btn layui-btn-normal layui-btn-sm"  id="order_statistics" lay-event="order_statistics">订单统计</a>
                <a class="layui-btn layui-btn-normal layui-btn-sm"  id="member_list" lay-event="member_list">团队成员</a>
{/*                <a class="layui-btn layui-btn-normal layui-btn-sm"  id="member_statistics" lay-event="member_statistics">团队订单统计</a>*/}
            </script>
            <!--自定义模板-->
            <script type="text/html" id="user-info">
                <img src="{{d.avatar}}" style="height:60px;width: 60px" class="image-show">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>编号:{{d.user_sn}}</p>
                    <p>昵称:{{d.nickname}}</p>
                    <p>手机:{{d.mobile}}</p>
                </div>
            </script>
            <script type="text/html" id="level-info">
                {{d.level_name}}
            </script>
            <script type="text/html" id="earnings-wait">
                {{d.earnings.wait}}
            </script>
            <script type="text/html" id="earnings-success">
                {{d.earnings.success}}
            </script>
            <script type="text/html" id="earnings-fail">
                {{d.earnings.fail}}
            </script>
            <script type="text/html" id="earnest_money">
                {{d.earnest_money}}
            </script>

            <script type="text/html" id="user-distribution">
                {{#  if(d.is_freeze){ }}
                冻结
                {{#  } else { }}
                正常
                {{#  } }}
            </script>
            <script type="text/html" id="first-info">
                {{# if(d.first_leader_info != '系统'){}}
                <div class="layui-input-inline" style="text-align:left;width: 240px">
                    <p>{{d.first_leader_info.mobile}} {{d.first_leader_info.nickname}}</p>
                    <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">编号：{{d.first_leader_info.sn}}</p>
                    <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">店铺名：{{d.first_leader_info.distribution_agent_name}}</p>
                    <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">真实姓名：{{d.first_leader_info.distribution_real_name}}</p>
                </div>
                {{# }else{ }}
                {{d.first_leader_info}}
                {{# } }}
            </script>
            <script type="text/html" id="second-info">
                {{# if(d.second_leader_info != '系统'){}}
                <div class="layui-input-inline" style="text-align:left;width: 240px">
                    <p>{{d.second_leader_info.mobile}}</p>
                    <p style="width: 180px;text-overflow:ellipsis;overflow: hidden">昵称：{{d.second_leader_info.nickname}}</p>
                </div>
                {{# }else{ }}
                {{d.second_leader_info}}
                {{# } }}
            </script>
        </div>
    </div>
</div>
<style>
    .selected{
        background-color: #1E9FFF !important;
        border: 1px solid #1E9FFF !important;
    }
</style>

<script>
    // 全局存储搜索条件
    let searchParams = {};

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;

        var laydate = layui.laydate;
        //日期时间范围
        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
            , theme: '#1E9FFF'
        });
        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
            , theme: '#1E9FFF'
        });


        //监听搜索
        form.on('submit(date_search)', function(data){
            var activeBtn = $(data.elem);
            // 移除所有按钮的 layui-bg-blue 类
            $('.search_date_button').removeClass('layui-bg-blue');
            // 为当前按钮添加背景色
            activeBtn.addClass('layui-bg-blue');
            var field = data.field;
            field.quick_date = $('.search_date_button.layui-bg-blue').data('date');
            //执行重载
            table.reload('lists', {
                where: field,
                page: {curr: 1}
            });
        });

        form.on('submit(search)', function(data){
            searchParams = data.field; // 保存当前搜索条件
            // console.log(searchParams);
            // var field = data.field;
            searchParams.quick_date = $('.search_date_button.layui-bg-blue').data('date');
            //执行重载
            table.reload('lists', {
                where: searchParams,
                page: {curr: 1}
            });
        });

        //清空查询
        form.on('submit(reset)', function(){
            $('#keyword').val('');
            $('#level_id').val('all');
            $('#is_freeze').val('all');
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: [], page: {curr: 1}
            });
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("distribution.distribution_member/index")}?first_leader={$first_leader}' //数据接口
            ,method: 'post'
            ,page: true //开启分页
            ,cols: [[ //表头
                {field: 'id', title: 'ID', width: 100, sort:true}
                ,{templet: '#user-info', title: '用户信息', width:300}
                ,{templet: '#level-info', title: '账号类型', width:160}
                ,{field: 'distribution_agent_name', title: '店铺名', width: 160}
                ,{templet: '#first-info', title: '服务商', width: 200}
                ,{field: 'sum_money', title: '卖货佣金', width: 150, sort:true}
                ,{field: 'sum_total_price', title: '卖货金额', width: 150, sort:true}
                // ,{templet: '#earnings-success', title: '已入账佣金', width:120}
                // ,{templet: '#earnings-wait', title: '待结算佣金', width:120}
                // ,{templet: '#earnings-fail', title: '已失效佣金', width:120}
                // ,{templet: '#earnest_money', title: '保证金', width:120}
                ,{templet: '#user-distribution', title: '状态', width: 120}
                ,{field: 'pay_partner_account_code', title: '汇付商户号', width: 160}
                ,{field: 'distribution_time', title: '成为分销会员时间', width: 200}
                // ,{templet: '#second-info', title: '二级上级', width: 160}

                ,{title: '操作', toolbar: '#operate', width: 400}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        table.on('sort(lists)', function(obj){ // lists对应表格的lay-filter值[8](@ref)
            console.log("当前排序字段：" + obj.field);
            console.log("当前排序方式：" + obj.type);

            // 重新加载表格数据
            table.reload('lists', {
                initSort: obj, // 记录排序状态[4](@ref)
                where: {
                    ...searchParams,      // 展开搜索条件
                    sortField: obj.field,  // 排序字段
                    sortOrder: obj.type    // 排序方式（asc/desc）
                },
                page: { curr: 1 } // 重置到第一页[8](@ref)
            });
        });

        // 工具条事件
        table.on('tool(lists)', function(obj){
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if(layEvent === 'freeze'){ // 冻结资格
                layer.confirm('确定要冻结资格吗?', function(index){
                    layer.close(index);
                    like.ajax({
                        url: "{:url('distribution.distribution_member/isFreeze')}",
                        data: {user_id: obj.data.user_id,is_freeze:1},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if(layEvent === 'unfreeze'){ // 恢复资格
                layer.confirm('确定要恢复资格吗?', function(index){
                    layer.close(index);
                    like.ajax({
                        url: "{:url('distribution.distribution_member/isFreeze')}",
                        data: {user_id: obj.data.user_id,is_freeze:0},
                        type: "post",
                        success:function(res) {
                            if(res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if(layEvent === 'adjust'){ // 分销等级调整
                id = obj.data.user_id;
                // 分销等级调整
                layer.open({
                    type: 2
                    ,title: "分销等级调整"
                    ,content: "{:url('distribution.distribution_member/adjust')}?id=" + id
                    ,area: ["90%", "90%"]
                    ,btn: ["确定", "取消"]
                    ,yes: function(index, layero){
                        var iframeWindow = window["layui-layer-iframe" + index];
                        var submit = layero.find("iframe").contents().find("#formSubmit");
                        iframeWindow.layui.form.on("submit(formSubmit)", function(data){
                            like.ajax({
                                url: "{:url('distribution.distribution_member/adjust')}",
                                data: data.field,
                                type: "post",
                                success:function(res) {
                                    if(res.code === 1) {
                                        layui.layer.msg(res.msg);
                                        layer.close(index);
                                        table.reload("lists");
                                    }
                                }
                            });
                            return false;
                        });
                        submit.trigger("click");
                    }
                });
            }else if (layEvent === 'adjust_first_leader'){
                var id = obj.data.user_id;
                layer.open({
                    type: 2
                    ,title: '调整分佣上级'
                    ,content: '{:url("user.user/adjustSecondLeader")}?id='+ id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                            ,submit = layero.find('iframe').contents().find('#formSubmit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(formSubmit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user.user/adjustSecondLeader")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }else if (layEvent === 'order_statistics'){
                var id = obj.data.user_id;
                layer.open({
                    type: 2
                    ,title: '分销订单统计'
                    ,content: '{:url("distribution.distribution_order/index")}?level=1&user_id='+ id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                                ,submit = layero.find('iframe').contents().find('#formSubmit');
                        //监听提交
                        iframeWindow.layui.form.on('submit(formSubmit)', function(data){
                            var field = data.field;
                            $.ajax({
                                url:'{:url("user.user/adjustSecondLeader")}',
                                data:field,
                                type:"post",
                                success:function(res)
                                {
                                    if(res.code == 1)
                                    {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        });
                                        layer.close(index); //关闭弹层
                                        table.reload('user-lists'); //数据刷新
                                    }else{
                                        layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 2
                                            , time: 1000
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                })
            }else if(layEvent === 'member_statistics'){
                var id = obj.data.user_id;
                console.log('id会员信息')
                console.log(id)
                layer.open({
                    type: 2
                    ,title: '团队统计'
                    ,content: '{:url("distribution.distribution_order/index")}?user_data_type=user_team&user_id='+ id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                                ,submit = layero.find('iframe').contents().find('#formSubmit');
                        //监听提交

                        submit.trigger('click');
                    }
                })
            }else if(layEvent === 'member_list'){
                var id = obj.data.user_id;
                console.log('id会员信息')
                console.log(id)
                layer.open({
                    type: 2
                    ,title: '团队列表'
                    ,content: '{:url("distribution.distribution_member/index")}?first_leader='+ id
                    ,area: ['90%', '90%']
                    ,btn: ['确定', '取消']
                    ,yes: function(index, layero){
                        var iframeWindow = window['layui-layer-iframe'+ index]
                                ,submit = layero.find('iframe').contents().find('#formSubmit');
                        //监听提交

                        submit.trigger('click');
                    }
                })
            }
        });

        // 功能按钮
        $('#open').click(function() { // 开通分销会员
            layer.open({
                type: 2
                ,title: "开通分销会员"
                ,content: "{:url('distribution.distribution_member/open')}"
                ,area: ["90%", "90%"]
                ,btn: ["确定", "取消"]
                ,yes: function(index, layero){
                    var iframeWindow = window["layui-layer-iframe" + index];
                    var submit = layero.find("iframe").contents().find("#openSubmit");
                    iframeWindow.layui.form.on("submit(openSubmit)", function(data){
                        like.ajax({
                            url: "{:url('distribution.distribution_member/open')}",
                            data: data.field,
                            type: "post",
                            success:function(res) {
                                if(res.code === 1) {
                                    layui.layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload("lists");
                                }
                            }
                        });
                        return false;
                    });
                    submit.trigger("click");
                }
            });
        });

    });


</script>