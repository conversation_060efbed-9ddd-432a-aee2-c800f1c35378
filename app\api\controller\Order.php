<?php

namespace app\api\controller;

use app\api\logic\OrderInvoiceLogic;
use app\api\logic\OrderLogic;
use app\api\validate\OrderValidate;
use app\common\basics\Api;
use app\common\logic\OrderCommonLogic;
use app\common\server\JsonServer;
use think\App;


class  Order extends Api
{
    /**
     * @notes 下单
     * @return \think\response\Json
     * @throws \think\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function submitOrder()
    {
        $this->getdistribution($this->user_id);
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $post['distribution'] = $this->distribution;
        (new OrderValidate())->goCheck('add', $post);
        $order = OrderLogic::add($post);
        if (false === $order) {
            return JsonServer::error(OrderLogic::getError());
        }
        return JsonServer::success('下单成功!', $order);
    }

    /**
     * @notes 结算页数据
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function settlement()
    {
        $this->getdistribution($this->user_id);
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $result = OrderLogic::settlement($post);
        if($result === false) {
            return JsonServer::error(OrderLogic::getError(), [], 301);
        }
        return JsonServer::success('获取成功', $result);
    }




    /**
     * @notes 提交结算
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function preOrder()
    {
        $this->getdistribution($this->user_id);
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $post['distribution'] = $this->distribution;
        $result = OrderLogic::preOrder($post);
        if($result === false || OrderLogic::getError()) {
            return JsonServer::error(OrderLogic::getError(), [], 301);
        }
        return JsonServer::success('获取成功', $result);
    }

    /**
     * Notes: 新版提交订单
     * Author: Darren
     * DateTime: 2023/4/20 18:34
     * @return \think\response\Json
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addOrder()
    {
        $this->getdistribution($this->user_id);
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        $post['client'] = $this->client;
        $post['distribution'] = $this->distribution;
        (new OrderValidate())->goCheck('add', $post);
        $order_lists = OrderLogic::preOrder($post);
        if (!$order_lists || !isset($order_lists['shop'])){
            return JsonServer::error('购物车数据错误');
        }
        $order = OrderLogic::saveOrderAll($order_lists, $post);
        if (false === $order) {
            return JsonServer::error(OrderLogic::getError());
        }
        return JsonServer::success('下单成功!', $order);
    }



    /**
     * @notes 订单列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function lists()
    {
        $type = $this->request->get('type', 'all');
        $keywords = $this->request->get('keywords', '');
        $start_time = $this->request->get('start_time', '');
        $end_time = $this->request->get('end_time', '');
        $order_list = OrderLogic::getOrderList($this->user_id, $type, $this->page_no, $this->page_size,$keywords,$start_time,$end_time);
        return JsonServer::success('获取成功', $order_list);
    }

    /**
     * @notes 获取订单详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function getOrderDetail()
    {
        $get = $this->request->get();
        (new OrderValidate())->goCheck('detail', $get);
        $detail = OrderLogic::getOrderDetail($get['id'], $this->user_id);
        if ($detail){
            return JsonServer::success('获取成功', $detail);
        }else{
            return JsonServer::error('未获取订单');
        }

    }

    /**
     * @notes 修改订单地址
     * @return array|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function editAddress()
    {
        $order_id = $this->request->post('order_id');
        $address_id = $this->request->post('address_id');
        if (empty($order_id) || empty($address_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::editAddress($order_id, $address_id, $this->user_id);
    }

    /**
     * @notes 取消订单
     * @return array|\think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function cancel()
    {
        $order_id = $this->request->post('id');
        $reason_id = $this->request->post('reason_id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::cancel($order_id, $reason_id, $this->user_id);
    }

    /**
     * 催发货
     * @return \think\response\Json
     */
    public function urge()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        $order_info = \app\common\model\order\Order::where('id', $order_id)->find();
        if (empty($order_info) || $order_info['user_id'] != $this->user_id) {
            return JsonServer::error('未获取订单');
        }
        if($order_info['urge_time']){
            return JsonServer::error('已经提交过催发货');
        }
        $order_info['urge_time'] = time();
        $order_info->save();
        return JsonServer::success('催发货成功');
    }

    /**
     * @notes 确认收货
     * @return array|\think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:11 下午
     */
    public function confirm()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::confirm($order_id, $this->user_id);
    }

    /**
     * @notes 删除订单
     * @return array|\think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function del()
    {
        $order_id = $this->request->post('id');
        if (empty($order_id)) {
            return JsonServer::error('参数错误');
        }
        return OrderLogic::del($order_id, $this->user_id);
    }

    /**
     * @notes 订单支付结果页面数据
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function pay_result()
    {
        $id = $this->request->get('id');
        $from = $this->request->get('from');//标识：trade：父订单，order：子订单
        $result = OrderLogic::pay_result($id,$from);
        if ($result !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }

    /**
     * @notes 获取支付方式
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function getPayWay()
    {
        $params = $this->request->get();
        if(!isset($params['from']) || !isset($params['order_id'])) {
            return JsonServer::error('参数缺失');
        }
        $pay_way = OrderLogic::getPayWay($this->user_id, $this->client, $params);
        return JsonServer::success('', $pay_way);
    }

    /**
     * @notes 物流查询
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2021/7/13 6:12 下午
     */
    public function orderTraces()
    {
        $order_id = $this->request->get('id');
        $delivery_id = $this->request->get('delivery_id', 0);
        $tips = '参数错误';
        if ($order_id) {
            $traces = OrderLogic::orderTraces($order_id, $this->user_id, $delivery_id);
            if ($traces) {
                return JsonServer::success('获取成功', $traces);
            }
            $tips = '暂无物流信息';
        }
        return JsonServer::error($tips);
    }

    /**
     * @notes PC获取支付状态
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/10/29 3:52 下午
     */
    public function getPayStatus()
    {
        $id = $this->request->get('id');
        $from = $this->request->get('from');//标识：trade：父订单，order：子订单
        $result = OrderLogic::getPayStatus($id,$from);
        if ($result !== false) {
            return JsonServer::success('', $result);
        } else {
            return JsonServer::error('参数错误');
        }
    }



    /**
     * @notes 发票详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2022/4/12 9:25
     */
    public function invoice()
    {
        $id = $this->request->get('id');
        $result = OrderInvoiceLogic::getInvoiceDetailByOrderId($id);
        return JsonServer::success('获取成功', $result);
    }

    public function platformCommission()
    {
        $id = $this->request->get('id');
        $orderList = OrderCommonLogic::getOrderPlatformCommissionOrderLists();
        dd($orderList);
    }

}
