<?php
namespace app\api\controller;

use app\admin\logic\distribution\ApplyLogic;
use app\api\logic\DistributionLogic;
use app\api\logic\ShareLogic;
use app\common\basics\Api;
use app\common\enum\DistributionLevel as LevelEnum;
use app\common\model\distribution\DistributionMemberApply;
use app\common\model\order\Order;
use app\common\model\user\UserShare;
use app\common\server\JsonServer;
use think\exception\ValidateException;
use app\api\validate\DistributionValidate;
use app\common\model\user\User;
use think\response\Json;
use app\common\model\distribution\Distribution as DistributionModel;

class Distribution extends Api
{
    public $like_not_need_login = ['fixAncestorRelation'];
    /**
     * 申请分销会员
     */
    public function apply()
    {
        if($this->request->isPost()){
            $post = $this->request->post();
            $post['user_id'] = $this->user_info ? $this->user_info['id'] : 0;
            try{
                validate(DistributionValidate::class)->scene('apply')->check($post);
            }catch(ValidateException $e) {
                return JsonServer::error($e->getError());
            }
            $result = DistributionLogic::apply($post);
            if($result) {
//                UserShare::where('random_code', $post['random_code'])->inc('use_number', 1)->update();
                return JsonServer::success('申请成功');
            }
            return JsonServer::error('申请失败');
        }else{
            return JsonServer::error('请求方式错误');
        }
    }

    /**
     * Notes: 申请重新激活店铺
     * Author: Darren
     * DateTime: 2023-05-30 16:38
     */
    public function reactivate(){
        $user_id = $this->user_id;
        $distribution = DistributionModel::where('user_id', $user_id)->find();
        if (!$distribution || !$distribution['is_distribution']){
            return JsonServer::error('分销商信息不存在或者不是分销商');
        }
        $apply = DistributionMemberApply::where('user_id', $user_id)->order('id', 'desc')->find();
        if ($apply){
            DistributionMemberApply::update(['reason'=>'激活店铺', 'status'=>0, 'update_time'=>time()], ['id'=>$apply['id']]);
            return JsonServer::success('申请成功');
        }else{
            return JsonServer::error('分销商入驻信息不存在');
        }
    }

    /**
     * 判断是否为分销会员
     */
    public function check()
    {
        $distribution = DistributionModel::where('user_id', $this->user_id)->findOrEmpty()->toArray();
        $limit_user_total_order_amount = config('user.limit_user_total_order_amount');
        if ($limit_user_total_order_amount > 0){
            //查询累计消费金额
//            $user_total_order_amount = Order::where('pay_status', '=', 1)->where('user_id', '=', $this->user_id)->value('sum(order_amount) as user_total_order_amount');
//            if ($user_total_order_amount < $limit_user_total_order_amount){
//                return JsonServer::error('累计消费金额满'.$limit_user_total_order_amount.'元才能开店哦');
//            }
        }
        if (!empty($distribution) && $distribution['is_distribution'] == 1) {
            return JsonServer::success('分销会员', [], 10001);
        }
        return JsonServer::success('非分销会员', [], 20001);
    }

    /**
     * 最新分销申请详情
     */
    public function applyDetail()
    {
        return JsonServer::success('获取成功', DistributionLogic::applyDetail($this->user_id));
    }


    /**
     * Notes:设置分销店铺信息
     */
    public function setInfo()
    {
        try{
            $post = $this->request->post();
            $post['user_id'] = $this->user_id;
            $post['distribution_id'] = $this->request->header('distributionid');
            $result = DistributionLogic::setInfo($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        if(!$result['error']) {
            return JsonServer::success('设置成功');
        }else{
            return JsonServer::error($result['error']);
        }
    }

    /**
     * 分销主页
     */
    public function index()
    {
        return JsonServer::success('获取成功', DistributionLogic::index($this->user_id));
    }

    /**
     * Notes:团队数据销售列表,包括自己的团队及自己孵化的团队
     * Author: Darren
     * DateTime: 2023-07-13 15:37
     */
    public function teamlist(){
        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        return JsonServer::success('获取成功', DistributionLogic::teamlist($this->user_id, $get, $this->user_info));
    }

    public function audit()
    {
        try{
            $distribution = DistributionModel::where('user_id', $this->user_id)->findOrEmpty()->toArray();
            if ($distribution){
                if ($distribution['is_distribution'] != 1  || $distribution['invite_open_shop'] != 1){
                    return JsonServer::error('无权限操作审核');
                }
            }else{
                return JsonServer::error('无权限操作审核');
            }
            $post = $this->request->post();
            $post['level_id'] = LevelEnum::LEVEL_2;
            $result_flag = ApplyLogic::audit($post);
        }catch(ValidateException $e) {
            return JsonServer::error($e->getError());
        }
        if($result_flag) {
            return JsonServer::success('提交成功');
        }else{
            return JsonServer::error('提交失败');
        }
    }

    public function distributiongoods(){
        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $distribution_info = DistributionModel::where('user_id', $this->user_id)
            ->where('is_distribution', '=', 1)
            ->where('is_freeze', '=', 0)->find();
        if (!$distribution_info){
            return JsonServer::error('暂无权限');
        }
        return JsonServer::success('获取成功', DistributionLogic::getDistributionGoods($get, $distribution_info));
    }


    /**
     * 填写邀请码
     */
    public function code()
    {
        $code = $this->request->post('code');
        $data = [
            'user_id' => $this->user_id,
            'code' => $code,
        ];

        try{
            validate(DistributionValidate::class)->scene('code')->check($data);
        }catch(ValidateException $e){
            return JsonServer::error($e->getError(), [], 0, 0);
        }
        $result = DistributionLogic::code($data);
        if($result) {
            return JsonServer::success('绑定上级成功');
        }
        return JsonServer::error(DistributionLogic::getError(), [], 0, 0);
    }

    /**
     * 分销订单
     */
    public function order()
    {
        $get = $this->request->get();
        $get['user_id'] = $this->user_id;
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        return JsonServer::success('获取成功', DistributionLogic::order($get));
    }

    /**
     * 月度账单
     */
    public function monthBill()
    {
        $get = $this->request->get();
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        return JsonServer::success('获取成功', DistributionLogic::monthBill($get));
    }

    /**
     * 月度账单明细
     */
    public function monthDetail()
    {
        $get = $this->request->get();
        if(!isset($get['year'])) {
            return JsonServer::error('年份参数不存在');
        }
        if(!isset($get['month'])) {
            return JsonServer::error('月份参数不存在');
        }
        $get['page_no'] = $this->page_no;
        $get['page_size'] = $this->page_size;
        $get['user_id'] = $this->user_id;
        return JsonServer::success('获取成功', DistributionLogic::monthDetail($get));
    }

    /**
     * 自身及上级信息
     */
    public function myLeader()
    {
        $data = DistributionLogic::myLeader($this->user_id);
        $data['distribution'] = $this::getdistribution($this->user_id, 0);
        if ($data['distribution']){
//            $url_data=[
//                'id'=>$data['distribution']['id'],
//                'type'=>3,
//                'distributionId'=>$data['distribution']['id'] ?? 0,
//                'invite_code'=>$data['distribution']['invite_code'] ?? ''
//            ];
//            $data['mini_url'] = ShareLogic::getWechatUrllink($data['distribution']['user_id'], $url_data);
            return JsonServer::success('获取成功', $data);
        }else{
            return JsonServer::success('获取成功', $data, -100);
        }
    }

    /**
     * @Notes: 佣金明细
     * @Author: 张无忌
     */
    public function commission()
    {
        $get = $this->request->get();
        $lists = DistributionLogic::commission($get, $this->user_id);
        if ($lists === false) {
            $message = DistributionLogic::getError() ?: '获取失败';
            return JsonServer::error($message);
        }

        return JsonServer::success('获取成功', $lists);
    }

    /**
     * 修复旧的关系链
     */
    public function fixAncestorRelation()
    {
        $result = DistributionLogic::fixAncestorRelation();
        if ($result) {
            return JsonServer::success('修复成功');
        }
        return JsonServer::error(DistributionLogic::getError());
    }

    /**
     * @notes 获取分享店铺背景海报
     * @return Json
     * <AUTHOR>
     * @date 2021/11/29 11:35
     */
    public function getPoster()
    {
        $result = DistributionLogic::getPoster();
        $distributionid = $this->request->header('distributionid');
        $distribution = DistributionLogic::getdistribution($this->user_id, $distributionid);
        if ($distribution){
            $url_data=[
                'id'=>$distribution['id'],
                'type'=>2,
                'distributionId'=>$distribution['id'] ?? 0,
                'invite_code'=>$distribution['invite_code'] ?? ''
            ];
            $result['mini_url'] = ShareLogic::getWechatUrllink($distribution['user_id'], $url_data);
        }else{
            $result['mini_url'] ='';
        }
        return JsonServer::success('',$result);
    }

    public function getDistributionShare()
    {
        $distributionid = $this->request->header('distributionid');
        $type = $this->request->get('type', '1');//
        $url_type = $this->request->get('url_type', '2');//
        $target_id = $this->request->get('target_id');
        $query=[
            'type'=>$type,
            'url_type'=>$url_type,
        ];
        $rel_goods_link_arr = DistributionLogic::getDistributionShareGoodsLink([$target_id], $distributionid, $query);
        return JsonServer::success('', array_values($rel_goods_link_arr));
    }
}