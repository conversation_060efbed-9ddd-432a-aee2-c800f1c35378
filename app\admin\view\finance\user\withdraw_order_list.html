{layout name="layout1" /}
<style>
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--主体区域-->
        <div class="layui-card-body">
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <script type="text/html" id="order-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>订单编号：{{d.order_sn}}</p>
                    <p>支付单号：{{d.transaction_id}}</p>
                </div>
            </script>
            <script type="text/html" id="pay-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>支付方式：{{d.pay_way}}</p>
                    <p>支付时间：{{d.order_pay_time}}</p>
                </div>
            </script>
            <script type="text/html" id="user-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>{{d.user_info.mobile}}</p>
                    <p>昵称：{{d.user_info.nickname}}</p>
                </div>
            </script>
            <script type="text/html" id="money-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>佣金金额：{{d.money}}</p>
                    <p>佣金比例：{{d.ratio}}</p>
                    <p>佣金类型：
                        {{#  if(d.level == 1){ }}
                        B卖货佣金
                        {{#  } else { }}
                        A邀请佣金
                        {{#  } }}</p>
                </div>
            </script>
            <script type="text/html" id="goods-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>{{d.goods_name}}  </p>
                    <p>规格：{{d.spec_value}}；&nbsp; 数量：{{d.goods_num}} ；&nbsp;&nbsp; 金额：{{d.total_pay_price}} 元</p>
                </div>
            </script>
            <script type="text/html" id="earnings-info">
                <div class="layui-input-inline"  style="text-align: left;">
                    <p>佣金金额：{{d.money}}</p>
                    <p>结算时间：{{d.settlement_time}}</p>
                </div>
            </script>
        </div>
    </div>
</div>
<script>

    layui.config({
        version:"{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;
        // 数据表格渲染
        table.render({
            elem: '#lists'
            ,url: '{:url("finance.user/withdraw_order_list")}?id={$id}' //数据接口
            ,method: 'post'
            ,totalRow: true
            ,page: false
            ,cols: [[ //表头
                // {templet: '#shop-info', title: '商家信息', width:180}
                {templet: '#order-info', title: '订单信息', width:320, totalRowText:"合计:"}
                ,{templet: '#pay-info', title: '支付时间', width:240}
                , {field: 'total_pay_price', title: '总金额', align: 'center',width:110,totalRow: true}
                , {field: 'goods_num', title: '数量', align: 'center',width:110,totalRow: true}
                ,{templet: '#money-info', title: '佣金', width:200}
                // , {field: 'money', title: '佣金', align: 'center',width:110,totalRow: true}
                ,{templet: '#user-info', title: '买家信息', width:160}
                ,{templet: '#goods-info', title: '商品信息', width:400}
                // , {field: 'goods_name', title: '商品名称', align: 'center',width:400}
                , {field: 'settlement_time', title: '结算时间', align: 'center',width:200}
            ]]
            , text: {none: '暂无数据！'}
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                console.log('---------'+res.data.count);
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "total": res.data.total, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            ,done: function(res, curr, count){
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });


    });


</script>