{layout name="layout2" /}
<style>
    .layui-form {
        margin: 5px;
    }
    .layui-form-label {
        width: 120px;
        text-align: left;
        padding-left:30px;
    }
    .layui-input {
        width: 300px;
    }
    .layui-textarea {
        width: 300px;
    }
    .reqRed:before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
    .checkbox-width{
        width: 120px;
    }
</style>
<form class="layui-form">
    <input type="hidden" name="id" value="{$detail.id}" />
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">晋升等级名称</label>
        <div class="layui-input-block">
            <input type="text" name="name" value="{$detail.name}" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">等级级别</label>
        <div class="layui-inline">
            <input type="number" min="2" name="weights" value="{$detail.weights}"  required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            级
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">等级描述</label>
        <div class="layui-input-block">
            <textarea name="remark" class="layui-textarea">{$detail.remark}</textarea>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">卖货佣金比例</label>
        <div class="layui-inline">
            <input type="number" min="0" name="first_ratio" value="{$detail.first_ratio}" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            %
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">邀请佣金比例</label>
        <div class="layui-inline">
            <input type="number" min="0" name="second_ratio" value="{$detail.second_ratio}" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            %
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">考核天数</label>
        <div class="layui-inline">
            <input type="number" min="0" name="check_period" value="{$detail.check_period}" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">(考核周期内)销售数量</label>
        <div class="layui-inline">
            <input type="number" min="0" name="total_sale_num" value="{$detail.total_sale_num}"   autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">(考核周期内)销售金额</label>
        <div class="layui-inline">
            <input type="number" min="0" name="total_sale_money" value="{$detail.total_sale_money}"   autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">保证金金额</label>
        <div class="layui-inline">
            <input type="number" min="0" name="earnest_money" value="{$detail.earnest_money}"   autocomplete="off" class="layui-input" />
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <label class="layui-form-label reqRed">开店权限</label>
        <div class="layui-inline">
            <input type="radio" name="open_shop" value="1" {if $detail.open_shop == 1}checked{/if} title="有">
            <input type="radio" name="open_shop" value="2" {if $detail.open_shop == 2}checked{/if} title="无">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <label class="layui-form-label">邀请开店权限</label>
        <div class="layui-inline">
            <input type="radio" name="invite_open_shop" value="1" {if $detail.invite_open_shop == 1}checked{/if} title="有">
            <input type="radio" name="invite_open_shop" value="2" {if $detail.invite_open_shop == 2}checked{/if} title="无">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block layui-hide">
            <button class="layui-btn" lay-submit lay-filter="editSubmit" id="editSubmit">立即提交</button>
        </div>
    </div>
</form>

<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer;
    });
</script>
