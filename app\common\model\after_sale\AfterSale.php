<?php

namespace app\common\model\after_sale;

use app\common\basics\Models;
use app\common\enum\AfterSaleEnum;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\logic\JushuitanLogic;
use app\common\model\Client_;
use app\common\model\Delivery;
use app\common\model\goods\GoodsItem;
use app\common\model\Pay;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\model\order\Order;
use app\common\model\order\OrderGoods;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\facade\Log;

class AfterSale extends Models
{
    //售后状态
    const STATUS_APPLY_REFUND           = 0;//待商家审核
    const STATUS_REFUSE_REFUND          = 1;//商家拒绝
    const STATUS_WAIT_RETURN_GOODS      = 2;//商品待退货
    const STATUS_WAIT_RECEIVE_GOODS     = 3;//商家待收货
    const STATUS_REFUSE_RECEIVE_GOODS   = 4;//商家拒收货
    const STATUS_WAIT_REFUND            = 5;//等待退款
    const STATUS_SUCCESS_REFUND         = 6;//退款成功

    //退款类型
    const TYPE_ONLY_REFUND      = 0;//仅退款
    const TYPE_REFUND_RETURN    = 1;//退款退货

    public static $no_settlement = [
        AfterSale::STATUS_APPLY_REFUND,       // 申请退款
        AfterSale::STATUS_WAIT_RETURN_GOODS,  // 商品待退货
        AfterSale::STATUS_WAIT_RECEIVE_GOODS, // 商家待收货
        AfterSale::STATUS_REFUSE_REFUND,        // 商家拒绝
        AfterSale::STATUS_REFUSE_RECEIVE_GOODS,        // 商家拒收货
    ];

    public static $set_fail = [
        AfterSale::STATUS_WAIT_REFUND,    // 等待退款
        AfterSale::STATUS_SUCCESS_REFUND, // 退款成功
    ];

    /**
     * @notes 售后状态描述
     * @param $state
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getStatusDesc($state)
    {
        $data = [
            self::STATUS_APPLY_REFUND           => '待商家审核',
            self::STATUS_REFUSE_REFUND          => '商家拒绝',
            self::STATUS_WAIT_RETURN_GOODS      => '商品待退货',
            self::STATUS_WAIT_RECEIVE_GOODS     => '商家待收货',
            self::STATUS_REFUSE_RECEIVE_GOODS   => '商家拒收货',
            self::STATUS_WAIT_REFUND            => '等待退款',
            self::STATUS_SUCCESS_REFUND         => '退款成功',
        ];
        if ($state === true) {
            return $data;
        }
        return $data[$state] ?? '';
    }


    /**
     * @notes 售后类型描述
     * @param $type
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getRefundTypeDesc($type)
    {
        $data = [
            self::TYPE_ONLY_REFUND      => '仅退款',
            self::TYPE_REFUND_RETURN    => '退款退货',
        ];
        if ($type === true) {
            return $data;
        }
        return $data[$type] ?? '';
    }

    /**
     * @notes 取消原因
     * @return string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getCancelReasonLists($key = 0)
    {
        $data = [
            1=>'7天无理由退货',
            2=>'拍错、重拍、不喜欢',
            3=>'其他',
        ];
        if ($key){
            $result = $data[$key] ?? '未知';
        }else{
            foreach ($data as $k=>$value){
                $result[]=[
                    'key'=>$k,
                    'value' => $value
                ];
            }
        }
        return $result;
    }

    /**
     * @notes 仅退款的理由
     * @return string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getReasonsOnlyRefund($key=0)
    {
        $data = [
            1=>'拍错、多拍、不喜欢',
            2=>'尺寸问题',
            3=>'七天无理由',
            4=>'其他'
        ];
        if ($key){
            $result = $data[$key] ?? '未知';
        }else{
            foreach ($data as $k=>$value){
                $result[]=[
                    'key'=>$k,
                    'value' => $value
                ];
            }
        }
        return $result;
    }

    /**
     * Notes: 检测能否7天无理由退款
     * Author: Darren
     * DateTime: 2023-07-17 16:29
     */
    public static function checkAble7DayNoReason($reason_id, $refund_type,$order_goods){
        if ($order_goods['shipping_status'] != OrderEnum::SHIPPING_FINISH || !$order_goods['delivery_id'] || $order_goods['delivery_type'] != 1){
            return true;
        }
        //申请7天无理由的订单
        if ($refund_type == AfterSaleEnum::REFUND_TYPE_GOODS_AND_MONEY && $reason_id==2 || $refund_type == AfterSaleEnum::REFUND_TYPE_ONLY_MONEY && $reason_id==3){
            $delivery = Delivery::field('is_check,last_check_time')->where('id', $order_goods['delivery_id'])->find()->toArray();
            if (!$delivery['is_check'] || !$delivery['last_check_time']){
                return true;
            }
            $time = time();
            //订单签收超过7天
            $date_obj = date_diff(date_create(date('Y-m-d H:i:s', $time)), date_create($delivery['last_check_time']));
            if ($date_obj->days > 7){
                return false;
            }
        }
        return true;
    }

    /**
     * @notes 售后原因,退货退款的理由
     * @return string[]
     * <AUTHOR>
     * @date 2021/7/13 6:37 下午
     */
    public static function getReasonLists($key=0)
    {
        $data = [
            1=>'拍错、多拍、不喜欢',
            2=>'7天无理由退货',
            3=>'材质与商品描述不符',
            4=>'提货地点不符',
            5=>'卖家发错货',
            6=>'收到商品少件、破损、脏污等',
            7=>'质量问题',
            8=>'其他'
        ];
        if ($key){
            $result = $data[$key] ?? '未知';
        }else{
            foreach ($data as $k=>$value){
                $result[]=[
                    'key'=>$k,
                    'value' => $value
                ];
            }
        }
        return $result;
    }


    /**
     * @notes 预载入OrderGoods
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     * @date 2021/7/13 6:38 下午
     */
    public function orderGoods()
    {
        return $this->hasMany(OrderGoods::class, 'id', 'order_goods_id');
    }

    /**
     * @notes 预载入user
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:38 下午
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id')
            ->field('id,sn,nickname,avatar,mobile,sex,create_time');
    }

    /**
     * @notes 预载入order
     * @return AfterSale|\think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:38 下午
     */
    public function order()
    {
        return $this->hasOne(Order::class, 'id', 'order_id')
            ->field('id,shop_id,order_sn,total_amount,order_amount,pay_way,order_status');
    }


    /**
     * @notes 预载入after_sale_log
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     * @date 2021/7/13 6:39 下午
     */
    public function logs()
    {
        return $this->hasMany('after_sale_log', 'after_sale_id', 'id')->order('id desc');
    }

    /**
     * Notes: 与聚水潭字段对应
     * Author: Darren
     * DateTime: 2023-05-10 11:29
     */
    public static function jushuitanFiledsMatch($id){
        try {
            $after_info = AfterSale::where('id', $id)
                ->with(['user','order','orderGoods'])
                ->find()
                ->toArray();
            if (!$after_info || !isset($after_info['orderGoods'])){
                throw new \Exception('订单或订单商品不存在');
            }
            $shop_id = $after_info['order']['shop_id'] ?? 0;
            if ($shop_id){
                JushuitanLogic::shopconfig($shop_id);
            }else{
                return false;
            }
            $item_id_arr = array_column($after_info['orderGoods'], 'item_id');
            $item_code_list = GoodsItem::where([['id', 'in', $item_id_arr]])->column('bar_code,sync_item_id', 'id');
            //组织订单商品数据
            foreach ($after_info['orderGoods'] as $k=>$goods){
                $ju_order_goods[$k]=[
//                    'outer_oi_id'=>strval($after_info['id']),    //平台订单明细编号，存在则会按此作为唯一性判断，商品为组合装时需要上传订单的明细编号
                    'sku_id'=>strval($item_code_list[$goods['item_id']]['bar_code']) ?? '', //ERP中商品编码
//                    'shop_sku_id'=>strval($item_code_list[$goods['item_id']]['sync_item_id']), //店铺商品规格编码
                    'qty'=>$goods['goods_num'],
                    'amount'=>money($goods['total_pay_price']),
                    'type'=>'退货',//可选:退货，换货，其它，补发
                    'name'=>$goods['goods_name'],
                    'pic'=>$goods['image'],
                    'properties_value'=>$goods['spec_value'],
                ];
            }
            /**
             * WAIT_SELLER_AGREE:买家已经申请，等待卖家同意,
             * WAIT_BUYER_RETURN_GOODS:卖家已经同意，等待买家退货,
             * WAIT_SELLER_CONFIRM_GOODS:买家已经退货，等待卖家确认收货,
             * SELLER_REFUSE_BUYER:卖家拒绝售后,
             * CLOSED:售后关闭(售后单未确认前填写该状态erp的售后单自动作废),
             * SUCCESS:售后成功；
             */
            $shop_status_arr[AfterSaleEnum::STATUS_ING] = 'WAIT_SELLER_AGREE';
            $shop_status_arr[AfterSaleEnum::STATUS_MECHANT_REFUSED] = 'SELLER_REFUSE_BUYER';
            $shop_status_arr[AfterSaleEnum::STATUS_GOODS_RETURNED] = 'WAIT_BUYER_RETURN_GOODS';
            $shop_status_arr[AfterSaleEnum::STATUS_RECEIVE_GOODS] = 'WAIT_SELLER_CONFIRM_GOODS';
            $shop_status_arr[AfterSaleEnum::STATUS_MECHANT_REFUSED_GOODS] = 'SELLER_REFUSE_BUYER';
            $shop_status_arr[AfterSaleEnum::STATUS_WAITING] = 'WAIT_SELLER_CONFIRM_GOODS';
            $shop_status_arr[AfterSaleEnum::STATUS_COMPLETE] = 'SUCCESS';
            /**
             * BUYER_NOT_RECEIVED:买家未收到货,??
             * BUYER_RECEIVED:买家已收到货,
             * BUYER_RETURNED_GOODS:买家已退货,
             * SELLER_RECEIVED:卖家已收到退货；可更新
             */
            if ($after_info['refund_type'] == 0){
                $good_status = 'BUYER_NOT_RECEIVED';
            }else{
                if ($after_info['status'] == AfterSaleEnum::STATUS_COMPLETE){
                    $good_status = 'SELLER_RECEIVED';
                }elseif($after_info['status'] == AfterSaleEnum::STATUS_RECEIVE_GOODS){
                    $good_status = 'BUYER_RETURNED_GOODS';
                }else{
                    $good_status = 'BUYER_RECEIVED';
                }
            }

            //组织订单数据
            $ju_order=[
                'shop_id'=>JushuitanLogic::$shop_info['sync_shop_id'],
                'outer_as_id'=>$after_info['sn'],
                'so_id'=>$after_info['order']['order_sn'],
                'type'=>'普通退货',//仅支持普通退货 售后类型，普通退货，其它，拒收退货,仅退款,投诉,补发,维修,换货
                'logistics_company'=>$after_info['express_name']?: '',//快递公司
                'l_id'=>$after_info['invoice_no'] ?: '',//物流单号
                'shop_status'=>$shop_status_arr[$after_info['status']],//售后状态
                'remark'=>$after_info['refund_remark'],//退款说明
                'good_status'=>$good_status,//退货商品状态
                'question_type'=>$after_info['refund_reason'],//问题类型；
                'total_amount'=>money($after_info['order']['order_amount']),//原销售订单总金额
                'refund'=>money($after_info['refund_price']),//	卖家应退金额
                'payment'=>0,//买家应补偿金额
                'freight'=>0,//卖家应退运费
                'items'=>$ju_order_goods,
                //
//                'receiver_state'=>'',//退货地址 省份
//                'receiver_city'=>'',//退货地址 省份
//                'receiver_district'=>'',//退货地址 省份
//                'receiver_address'=>'',//退货地址 省份
            ];
            return $ju_order;
        } catch (Exception $e) {
            Log::write('订单同步聚水潭失败:'.$e->getMessage());
            return ['error'=>$e->getMessage()];
        }
    }
}