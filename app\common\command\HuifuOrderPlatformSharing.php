<?php

namespace app\common\command;

use app\common\enum\DistributionEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\logic\OrderCommonLogic;
use app\common\model\AccountLog;
use app\common\model\after_sale\AfterSale;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\user\User;
use app\common\model\shop\Shop;
use app\common\server\ConfigServer;
use app\common\server\HuifuPayServer;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class HuifuOrderPlatformSharing extends Command
{
    protected function configure()
    {
        $this->setName('huifu_order_platform_sharing')
            ->setDescription('定时任务脚本描述: 销售订单的平台服务费/货款分账给对应商户。将平台服务费platform_commission分账给平台,货款(支付总金额-平台服务费-分销商的佣金)分账给供货商');
    }
    /**
     * 功能逻辑要点:
     * 1、查询可以结算的销售订单 Order 列表:
     * 2. 循环订单列表, 分别调用分账接口分账
     *      1)平台服务费商户信息是 config.pay.platform_distribution_huifu_id 配置的商户信息
     *      2) 供货商的货款商户信息是 依据订单表中shop_id查询商户信息
     * 3. 根据结果处理, 如果保存状态, 如果失败记录失败原因
     * 汇付分账接口 https://paas.huifu.com/open/doc/api/#/smzf/api_fz
     * @param Input $input
     * @param Output $output
     * @return bool
     */
    protected function execute(Input $input, Output $output)
    {
        echo 'start--HuifuOrderPlatformSharing-'.PHP_EOL;
        // 检查是否启用自动确认
        $is_auto_huifu_order_sharing = config('pay.is_auto_huifu_order_sharing');
        if ($is_auto_huifu_order_sharing != 1) {
            echo '未启用自动确认is_auto_huifu_order_sharing';
            return false;
        }
        //1、查询可以结算的销售订单 Order 列表:
        $orderList = OrderCommonLogic::getOrderPlatformCommissionOrderLists();
        if (!$orderList) {
            echo '没有需要处理的订单';
            return false;
        }
        $time = time();
        foreach ($orderList as $order) {
            try {
                    Db::startTrans();
                    try {
                        // 2. 调用汇付分账接口
                        $huifuServer = HuifuPayServer::getInstance();
                        $result = $this->executePlatformSharing($huifuServer, $order);
                        if ($result === false) {
                            throw new \Exception('分账失败: ' . $huifuServer->getError());
                        }
                        // 3. 更新订单状态
                        $order->platform_commission_status = 1; // 1已经分账收取
                        $order->save();
                        Db::commit();
                        echo 'Success processing id:' . $order['id'] . ' - platform_commission:' . $result['platform_commission'] . ' - supplier_amount:' . $result['supplier_amount'] . PHP_EOL;
                    } catch (\Exception $e) {
                        Db::rollback();
                        Log::write('汇付平台分账处理异常: order_id:'.$order['id'].' ' . $e->getMessage());
                        echo 'Error processing order_id:' . $order['id'] . ' - ' . $e->getMessage() . PHP_EOL;
                        // 更新订单状态为分账出错
                        $order->platform_commission_status = 99; // 99分账出错
                        $order->save();
                    }
                    // 避免请求过于频繁
                    sleep(1);

            } catch (\Exception $e) {
                Log::write('汇付平台分账结算异常:' . $e->getMessage());
            }
        }
        return true;
    }

    /**
     * 执行平台分账, 平台获得服务费, 供货商获得货款
     * 注意 服务费可能为0, 则不需要分服务费
     * 1、计算分账金额
     * 2、调用汇付分账接口
     * 3、根据结果处理, 如果保存状态, 如果失败记录失败原因
     * @param HuifuPayServer $huifuServer
     * @param array $order 订单信息
     * @return array|false 成功返回包含platform_commission和supplier_amount的数组，失败返回false
     */
    private function executePlatformSharing($huifuServer, $order)
    {
         // 获取供货商商户信息
         $shopInfo = Shop::where('id', $order['shop_id'])->find();
         if (!$shopInfo || !$shopInfo['pay_partner_account_code']) {
             throw new \Exception('供货商商户信息不存在');
         }
        // 计算分账金额并调用汇付分账接口
        $platform_commission = $order['platform_commission'] ?? 0; // 平台服务费
        // 获取平台服务费商户信息
        $platform_merchant_id = config('pay.platform_distribution_huifu_id');
        if (!$platform_merchant_id) {
            throw new \Exception('平台服务费商户号未配置');
        }
        $distribution_commission = DistributionOrderGoods::where([
            ['order_id', '=', $order['id']],
            ['status', 'in', [DistributionOrderGoodsEnum::STATUS_WAIT_HANDLE, DistributionOrderGoodsEnum::STATUS_SUCCESS]]
        ])->sum('money') ?? 0;
        $supplier_amount = $order['order_amount'] - $platform_commission - $distribution_commission;
        if ($supplier_amount < 0) {
            throw new \Exception('供货商货款计算错误，金额为负数');
        }
        // 组合汇付接口要使用的平台服务费分账账户信息及供货商货款分账账户信息
        $acct_infos[] = [
            'huifu_id'=>$shopInfo['pay_partner_account_code'],
            'div_amt'=>$supplier_amount,
        ];
        if ($platform_commission > 0) {
            $acct_infos[] = [
                'huifu_id'=>$platform_merchant_id,
                'div_amt'=>$platform_commission,
            ];
        }
        $acct_split_bunch =[
            'percentage_flag'=>'N',
            'total_div_amt'=> money($platform_commission + $supplier_amount),
            'acct_infos'=>$acct_infos
        ];
        //提前查询原来的分账信息
        // 检测是否已经有分账信息, 如果有则并且分账商户号相同, 金额相等, 则不重复分账
        $huifu_finished_split_order = $huifuServer->transSplitQuery($order);
        if ($huifu_finished_split_order) {
            foreach ($huifu_finished_split_order as $item) {
                foreach ($acct_split_bunch['acct_infos'] as $acct_info) {
                    if ($item['in_huifu_id'] == $acct_info['huifu_id'] && (abs($item['split_amt'] - $acct_info['div_amt']) < 0.01)) {
                        $huifuServer->error = $acct_info['huifu_id'] . '已分账' . $acct_info['div_amt'] . '元';
                        return false;
                    }
                }
            }
        }
        // 调用platformSharing方法 执行分账
        $result = $huifuServer->platformSharing($order, $acct_split_bunch);
        if ($result && isset($result['data']['resp_code']) && in_array($result['data']['resp_code'], ['00000000', '00000100'])) {
            Log::write('平台分账成功-订单ID:' . $order['id'] . ' - 结果:' . json_encode($result));
            return [
                'platform_commission' => $platform_commission,
                'supplier_amount' => $supplier_amount,
            ];
        } else {
            $huifuServer->error = $result['data']['resp_desc'] ?? '分账失败';
            return false;
        }
    }
}
