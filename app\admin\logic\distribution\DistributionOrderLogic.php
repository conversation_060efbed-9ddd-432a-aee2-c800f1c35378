<?php
namespace app\admin\logic\distribution;

use app\common\basics\Logic;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use app\common\server\HuifuPayServer;
use app\common\server\UrlServer;

class DistributionOrderLogic extends Logic
{
    /**
     * @notes 分销订单列表
     * @param $params
     * @return int[]
     * <AUTHOR>
     * @date 2021/9/3 14:53
     */
    public static function lists($params, $is_export = false)
    {
        $where= [];
        // 订单信息
        if (isset($params['order_keyword']) && !empty($params['order_keyword'])) {
            $order_id = Order::where('order_sn', $params['order_keyword'])->value('id');
            $where[] = ['dog.order_id', '=', $order_id];
        }
        // 商品名称
        if (isset($params['goods_keyword']) && !empty($params['goods_keyword'])) {
            $where[] = ['og.goods_name', 'like', '%'.$params['goods_keyword'].'%'];
        }
        // 分销会员
        if (isset($params['distribution_keyword']) && !empty($params['distribution_keyword'])) {
            $where[] = ['u.sn|u.nickname', 'like', '%'.$params['distribution_keyword'].'%'];
        }
        if (isset($params['distribution_mobile']) && !empty($params['distribution_mobile'])) {
            $where[] = ['u.mobile', 'like', '%'.$params['distribution_mobile'].'%'];
        }
        // 佣金状态
        if (isset($params['status']) && !empty($params['status'])) {
            $where[] = ['dog.status', 'in', $params['status']];
        }
        $current_user_id = 0;
        if (isset($params['user_id']) && !empty($params['user_id'])) {
            $where[] = ['u.id', '=', $params['user_id']];
            $current_user_id = $params['user_id'];
        }
        if (isset($params['level']) && !empty($params['level'])) {
            $where[] = ['dog.level', '=', $params['level']];
        }
        if (isset($params['quick_date']) && !empty($params['quick_date'])) {
            if ($params['quick_date'] == 'today'){
                $start_time = strtotime('today 00:00:00');
                $end_time = strtotime('today 23:59:59');
            }elseif($params['quick_date'] == 'yesterday'){
                $start_time = strtotime('yesterday 00:00:00');
                $end_time = strtotime('yesterday 23:59:59');
            }elseif($params['quick_date'] == 'this_month'){
                $start_time = strtotime(date('Y-m-01 00:00:00'));
                $end_time = strtotime(date('Y-m-01 00:00:00', strtotime('+1 month')) . ' -1 second');
            }elseif($params['quick_date'] == 'last_month'){
                $start_time = strtotime('first day of last month 00:00:00');
                $end_time = strtotime('last day of last month 23:59:59');
            }elseif($params['quick_date'] == 'all'){
                $start_time = 0;
                $end_time = 0;
            }
            if ($start_time && $end_time){
                $where[] = ['dog.create_time', 'between', [$start_time, $end_time]];
            }
        }else{
            $start_time = isset($params['start_time']) && $params['start_time'] ? strtotime($params['start_time']) : 0;
            $end_time = isset($params['end_time']) && $params['end_time'] ? strtotime($params['end_time']) : 0;
            if ($start_time && $end_time) {
                $where[] = ['dog.create_time', 'between', [$start_time, $end_time]];
            }else if($start_time){
                $where[] = ['dog.create_time', '>=', $start_time];
            }else if($end_time){
                $where[] = ['dog.create_time', '<=', $end_time];
            }
        }
        $sort_way = $get['sort_way'] ?? 'desc';
        // 导出excel
        if (true === $is_export) {
            return self::export($where, $sort_way);
        }
        $field = [
//            'o.id' => 'order_id',
//            'o.order_sn',
//            'o.order_status',
//            'o.shipping_status',
//            'o.create_time' => 'order_create_time',
//            'o.shipping_time',
//            'o.all_comment_time',
//            'o.user_id' => 'order_user_id',
            'u.id' => 'distribution_user_id',
            'u.avatar' => 'distribution_avatar',
            'u.mobile' => 'mobile',
            'u.sn' => 'distribution_sn',
            'u.nickname' => 'distribution_nickname',
            'og.image' => 'goods_image',
            'og.goods_name' => 'goods_name',
            'og.spec_value' => 'spec_value',
            'og.goods_num' => 'goods_num',
            'og.total_pay_price' => 'total_pay_price',
            'dog.order_id',
            'dog.buyer_user_id',
            'dog.level_id',
            'dog.level',
            'dog.ratio',
            'dog.money',
            'dog.user_id',
            'dog.status',
            'dog.status' => 'status_desc',
            'dog.settlement_time',
            'dog.comment_time',
            'dog.confirm_take_time',
            'dog.remark',
            'dog.distribution_commission_method',
            's.id' => 'shop_id',
            's.name' => 'shop_name',
            's.logo' => 'shop_logo',
        ];
        $lists = DistributionOrderGoods::alias('dog')
            ->leftJoin('user u', 'u.id = dog.user_id')
            ->leftJoin('order_goods og', 'og.id = dog.order_goods_id')
            ->leftJoin('distribution_level dl', 'dl.id = dog.level_id')
            ->leftJoin('shop s', 's.id = dog.shop_id')
            ->field($field)
            ->where($where)
            ->order('dog.create_time', 'desc')
            ->page($params['page'], $params['limit'])
//            ->fetchSQL()
            ->select()
            ->toArray();
        $count = DistributionOrderGoods::alias('dog')
            ->leftJoin('user u', 'u.id = dog.user_id')
            ->leftJoin('order_goods og', 'og.id = dog.order_goods_id')
            ->leftJoin('distribution_level dl', 'dl.id = dog.level_id')
            ->leftJoin('shop s', 's.id = dog.shop_id')
//            ->field($field)
            ->where($where)
            ->count();

        $statistics_all_default = DistributionOrderGoodsEnum::getOrderStatus(true);
        $statistics_all_data =[];
        foreach ($statistics_all_default as $key => $item) {
            $statistics_all_data['status_'.$key]['status_text'] =  $item;
            $statistics_all_data['status_'.$key]['total_price'] = 0;
            $statistics_all_data['status_'.$key]['money'] = 0;
            $statistics_all_data['status_'.$key]['status'] = 0;
        }
        $statistics_all_data['statistics_total_distribution_money'] = 0;
        $statistics_all_data['statistics_total_amount'] = 0;
        if (!$count){
            return [
                'count' => $count,
                'lists' => $lists,
                'statistics'=>$statistics_all_data,
                'statistics_total_amount'=>0,
            ];
        }
        $order_id_arr = array_column($lists, 'order_id');
        $order_list = Order::where('id', 'in', $order_id_arr)->column('order_sn,order_status,shipping_status,create_time as order_create_time,shipping_time,all_comment_time,user_id as order_user_id', 'id');
//        foreach ($order_list as &$item){
//            $item['order_create_time'] = $item['order_create_time'] ? date('Y-m-d H:i:s', $item['order_create_time']) : '';
//            $item['shipping_time'] = $item['shipping_time'] ? date('Y-m-d H:i:s', $item['shipping_time']) : '';
//            $item['all_comment_time'] = $item['all_comment_time'] ? date('Y-m-d H:i:s', $item['all_comment_time']) : '';
//        }
        $distribution_user_id_arr = array_values(array_unique(array_column($lists, 'distribution_user_id')));
        $distribution_user_shop_list = Distribution::where('user_id', 'in', $distribution_user_id_arr)->column('id,user_id,distribution_agent_name,real_name,level_id,pay_partner_account_code', 'user_id');
        $buyer_user_id_arr = array_values(array_unique(array_column($lists, 'buyer_user_id')));
        $buyer_user_list = User::where('id', 'in', $buyer_user_id_arr)->column('id,sn,mobile,nickname,avatar', 'id');
        $level_name_list = DistributionLevel::column('id,name', 'id');
        foreach ($lists as &$list){
            if (isset($buyer_user_list[$list['buyer_user_id']]) && $buyer_user_list[$list['buyer_user_id']]){
                $buyer_user_list[$list['buyer_user_id']]['avatar'] = UrlServer::getFileUrl($buyer_user_list[$list['buyer_user_id']]['avatar']);
                $list['user_info'] = $buyer_user_list[$list['buyer_user_id']];
            }
            $list['level_name'] = $list['level_id'] && isset($level_name_list[$list['level_id']]['name']) ?  $level_name_list[$list['level_id']]['name'] : '';
            $list['distribution_agent_name'] = $distribution_user_shop_list[$list['distribution_user_id']]['distribution_agent_name'] ?? '';
            $list = array_merge($list, $order_list[$list['order_id']] ?? []);
        }
        $statistics = DistributionOrderGoods::alias('dog')
            ->leftJoin('user u', 'u.id = dog.user_id')
            ->leftJoin('order_goods og', 'og.id = dog.order_goods_id')
            ->leftJoin('distribution_level dl', 'dl.id = dog.level_id')
            ->leftJoin('shop s', 's.id = dog.shop_id')
            ->field('sum(dog.total_price) as total_price, dog.status, sum(dog.money) as money')
            ->where($where)
            ->group('dog.status')
            ->select()
            ->toArray();
        $statistics = array_column($statistics, null, 'status');
        foreach ($statistics_all_default as $key => $item) {
            $statistics_all_data['status_'.$key]['status_text'] =  $item;
            $statistics_all_data['status_'.$key]['money'] = $statistics[$key]['money'] ?? 0;
            $statistics_all_data['status_'.$key]['status'] = $statistics[$key]['status'] ?? 0;
            $statistics_all_data['statistics_total_amount'] += $statistics[$key]['total_price'] ?? 0;
        }
        $statistics_all_data['statistics_total_amount'] = money($statistics_all_data['statistics_total_amount']);
        // 订单自动收货时间
        $order_auto_receipt_seconds = intval(ConfigServer::get('transaction', 'order_auto_receipt_days', '7')) * 86400;
//        订单收货后多少天自动评价
        $order_auto_comment_seconds = intval(ConfigServer::get('transaction', 'order_auto_comment_days', '7')) * 86400;
//        订单评价后多少天返佣金
        $user_commission_pay_seconds = intval(ConfigServer::get('transaction', 'user_commission_pay_days', '7')) * 86400;
        foreach($lists as &$item) {
            $item['distribution_avatar'] = empty($item['distribution_avatar']) ? '' : UrlServer::getFileUrl($item['distribution_avatar']);
//            $item['user_info']['avatar'] = empty($item['user_info']['avatar']) ? '' : UrlServer::getFileUrl($item['user_info']['avatar']);
//            $item['level_name'] = DistributionLevel::getLevelName($item['level_id']);
//            $item['distribution_agent_name'] = Distribution::where('user_id', $item['user_id'])->value('distribution_agent_name');
            $item['shop_logo'] = empty($item['shop_logo']) ? '' : UrlServer::getFileUrl($item['shop_logo']);
            $item['goods_image'] = empty($item['goods_image']) ? '' : UrlServer::getFileUrl($item['goods_image']);
            //预计结算时间
            if (!$item['settlement_time'] && $item['status'] != 3){
                if ($item['comment_time']){
                    $item['expect_settlement_time'] = $item['comment_time'] + $user_commission_pay_seconds;
                }else if($item['confirm_take_time']){
                    $item['expect_settlement_time'] = $item['confirm_take_time'] + $user_commission_pay_seconds + $order_auto_comment_seconds;
                }else if($item['shipping_time']){
                    $item['expect_settlement_time'] = $item['shipping_time'] + $user_commission_pay_seconds + $order_auto_comment_seconds + $order_auto_receipt_seconds;
                }else{
                    $item['expect_settlement_time'] = '';
                }
            }else{
                $item['expect_settlement_time'] = '';
            }
            $item['order_status'] = Order::getOrderStatus($item['order_status']);
            $item['shipping_status'] = Order::getShippingStatus($item['shipping_status']);
            $item = datetime_format_array($item);
        }
        $statistics_all_data['statistics_total_distribution_money'] = $statistics_all_data['status_1']['money'] +
            $statistics_all_data['status_2']['money'] +
            $statistics_all_data['status_4']['money'] +
            $statistics_all_data['status_5']['money'];
        $statistics_all_data['statistics_total_distribution_money'] = money($statistics_all_data['statistics_total_distribution_money']);
        $huifu_avl_bal_total =0;
        if ($current_user_id && isset($distribution_user_shop_list[$current_user_id]['pay_partner_account_code']) && $distribution_user_shop_list[$current_user_id]['pay_partner_account_code']){
            $huifuServer = HuifuPayServer::getInstance();
            $queryHuifu =[
                'huifu_id'=>$distribution_user_shop_list[$current_user_id]['pay_partner_account_code']
            ];
            $acctInfo = $huifuServer->userBalanceQuery($queryHuifu, 1);
            $huifu_avl_bal_total = $acctInfo['total'] ?? 0;
        }
        $statistics_all_data['huifu_avl_bal_total'] = money($huifu_avl_bal_total);
        return [
            'count' => $count,
            'lists' => $lists,
            'statistics'=>$statistics_all_data,
            'statistics_total_number_count'=>$count,
        ];
    }

//    /**
//     * 佣金导出
//     * @param $condition
//     * @param $sort_way
//     * @return array|false
//     */
//    public static function export($condition = [], $sort_way='asc')
//    {
//        try {
//            $field = 'o.*,order_status as order_status_text,pay_way as pay_way_text,
//            o.delivery_type as delivery_type_text,order_type as order_type_text,
//            u.nickname,s.name as shop_name,s.type as shop_type';
//
//            $lists = Order::alias('o')
//                ->leftjoin('shop s', 's.id = o.shop_id')
//                ->leftjoin('user u', 'u.id = o.user_id')
//                ->join('order_goods g', 'g.order_id = o.id')
//                ->with(['order_goods'])
//                ->field($field)
//                ->where($condition)
//                ->append(['delivery_address', 'pay_status_text', 'order_source_text'])
//                ->order('o.id '.$sort_way)
//                ->group('o.id')
//                ->select()
//                ->toArray();
//
//            if ($lists){
//                $trade_id_arr = array_column($lists, 'trade_id');
//                $t_sn_arr = OrderTrade::where('id', 'in', $trade_id_arr)->column('t_sn', 'id');
//            }
//
//            foreach ($lists as &$item) {
//                $orderGoodsList = [];
//                $goodsItemList = [];
//                $goodsPriceList = [];
//                $goodsNumList = [];
//                foreach ($item['order_goods'] as $good) {
//                    $orderGoodsList[] = $good['goods_name'];
//                    $goodsItemList[] = $good['spec_value'];
//                    $goodsPriceList[] = $good['goods_price'];
//                    $goodsNumList[] = $good['goods_num'];
//                }
//                $item['transaction_id'] = $item['transaction_id'] ?: OrderTrade::where('id','=', $item['trade_id'])->value('transaction_id');
//                $item['t_sn'] = isset($t_sn_arr[$item['trade_id']]) && $t_sn_arr[$item['trade_id']] ? '`'.$t_sn_arr[$item['trade_id']] : '';
//                $item['order_goods_list'] = implode(';', $orderGoodsList);
//                $item['goods_item_list'] = implode(';', $goodsItemList);
//                $item['goods_price_list'] = implode(';', $goodsPriceList);
//                $item['goods_num_list'] = implode(';', $goodsNumList);
//                $item['shop_type'] = ShopEnum::getShopTypeDesc($item['shop_type']);
//            }
//
//            $excelFields = [
//                'shop_name' => '商家名称',
//                't_sn' => '支付单号',
//                'order_sn' => '订单编号',
//                'transaction_id' => '支付流水',
//                'order_type_text' => '订单类型',
//                'nickname' => '用户名称',
//                'order_goods_list' => '商品信息',
//                'goods_item_list' => '规格',
//                'goods_price_list' => '商品价格',
//                'goods_num_list' => '商品数量',
//                'order_amount' => '实付金额',
//                'consignee' => '收货人',
//                'mobile' => '收货人手机',
//                'delivery_address' => '收货地址',
//                'pay_status_text' => '支付状态',
//                'order_status_text' => '订单状态',
//                'create_time' => '下单时间',
//            ];
//
//            $export = new ExportExcelServer();
//            $export->setFileName('订单');
//            $result = $export->createExcel($excelFields, $lists);
//
//            return ['url' => $result];
//
//        } catch (\Exception $e) {
//            self::$error = $e->getMessage();
//            return false;
//        }
//    }
}