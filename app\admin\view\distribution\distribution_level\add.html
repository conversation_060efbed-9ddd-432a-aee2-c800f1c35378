{layout name="layout2" /}
<style>
    .layui-form {
        margin: 5px;
    }
    .layui-form-label {
        width: 120px;
        text-align: left;
        padding-left:30px;
    }
    .layui-input {
        width: 300px;
    }
    .layui-textarea {
        width: 300px;
    }
    .reqRed:before {
        content: '*';
        color: red;
        margin-right: 5px;
    }
    .checkbox-width{
        width: 120px;
    }
</style>
<form class="layui-form">
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">等级名称</label>
        <div class="layui-input-block">
            <input type="text" name="name" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">等级级别</label>
        <div class="layui-inline">
            <input type="number" min="2" name="weights" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            级
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">等级描述</label>
        <div class="layui-input-block">
            <textarea name="remark" class="layui-textarea"></textarea>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">卖货佣金比例</label>
        <div class="layui-inline">
            <input type="number" min="0" name="first_ratio" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            %
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label reqRed">邀请佣金比例</label>
        <div class="layui-inline">
            <input type="number" min="0" name="second_ratio" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
        <div class="layui-inline">
            %
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">考核天数</label>
        <div class="layui-inline">
            <input type="number" min="0" name="check_period" value="0" required  lay-verify="required" autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">(考核周期内)销售数量</label>
        <div class="layui-inline">
            <input type="number" min="0" name="total_sale_num" value="0"   autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">(考核周期内)销售金额</label>
        <div class="layui-inline">
            <input type="number" min="0" name="total_sale_money" value="0"   autocomplete="off" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">保证金金额</label>
        <div class="layui-inline">
            <input type="number" min="0" name="earnest_money" value="0"   autocomplete="off" class="layui-input" />
        </div>
    </div>

    <div class="layui-form-item  layui-hide">
        <label class="layui-form-label reqRed">开店权限</label>
        <div class="layui-inline">
            <input type="radio" name="open_shop" value="1"  title="有">
            <input type="radio" name="open_shop" value="2"  title="无">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <label class="layui-form-label">邀请开店权限</label>
        <div class="layui-inline">
            <input type="radio" name="invite_open_shop" value="1"  title="有">
            <input type="radio" name="invite_open_shop" value="2"  title="无">
        </div>
    </div>


<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label reqRed">等级条件</label>-->
<!--        <div class="layui-inline">-->
<!--            <input type="radio" name="update_relation" value="1" title="满足以下任一条件">-->
<!--            <input type="radio" name="update_relation" value="2" title="满足以下全部条件">-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label"></label>-->
<!--        <div class="layui-inline checkbox-width">-->
<!--            <input type="checkbox" lay-skin="primary" name="update_condition[]" value="cumulativeNumber" title="购买件数">-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            <input type="number" min="0" name="singleConsumptionAmount" class="layui-input" />-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            件-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label"></label>-->
<!--        <div class="layui-inline checkbox-width">-->
<!--            <input type="checkbox" lay-skin="primary" name="update_condition[]" value="cumulativeConsumptionAmount" title="累计消费金额">-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            <input type="number" min="0" name="cumulativeConsumptionAmount" class="layui-input" />-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            元-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label"></label>-->
<!--        <div class="layui-inline checkbox-width">-->
<!--            <input type="checkbox" lay-skin="primary" name="update_condition[]" value="singleConsumptionAmount" title="单笔消费金额">-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            <input type="number" min="0" name="singleConsumptionAmount" class="layui-input" />-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            元-->
<!--        </div>-->
<!--    </div>-->

<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label"></label>-->
<!--        <div class="layui-inline checkbox-width">-->
<!--            <input type="checkbox" lay-skin="primary" name="update_condition[]" value="cumulativeConsumptionTimes" title="累计消费次数">-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            <input type="number" min="0" name="cumulativeConsumptionTimes" class="layui-input" />-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            次-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="layui-form-item">-->
<!--        <label class="layui-form-label"></label>-->
<!--        <div class="layui-inline checkbox-width">-->
<!--            <input type="checkbox" lay-skin="primary" name="update_condition[]" value="returnedCommission" title="已结算佣金收入">-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            <input type="number" min="0" name="returnedCommission" class="layui-input" />-->
<!--        </div>-->
<!--        <div class="layui-inline">-->
<!--            元-->
<!--        </div>-->
<!--    </div>-->
    <div class="layui-form-item">
        <div class="layui-input-block layui-hide">
            <button class="layui-btn" lay-submit lay-filter="addSubmit" id="addSubmit">立即提交</button>
        </div>
    </div>
</form>

<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['element', 'form'], function () {
        var $ = layui.$
            , form = layui.form
            , layer = layui.layer;
    });
</script>
