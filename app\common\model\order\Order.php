<?php

namespace app\common\model\order;

use app\common\basics\Models;
use app\common\enum\OrderEnum;
use app\common\enum\OrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\logic\JushuitanLogic;
use app\common\model\AfterSale;
use app\common\model\Client_;
use app\common\model\distribution\Distribution;
use app\common\model\Express;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsItem;
use app\common\model\Pay;
use app\common\model\shop\Shop;
use app\common\model\user\User;
use app\common\model\user\UserAuth;
use app\common\server\ConfigServer;
use think\facade\Db;
use think\facade\Log;

/**
 * Class order
 * @package app\common\model\order
 */
class Order extends Models
{
    //订单状态
    const STATUS_WAIT_PAY = 0;       //待付款
    const STATUS_WAIT_DELIVERY = 1;  //待发货
    const STATUS_WAIT_RECEIVE = 2;   //待收货
    const STATUS_FINISH = 3;         //已完成
    const STATUS_CLOSE = 4;          //已关闭
    const STATUS_PART_DELIVERY = 5;   //部分发货
    const DISTRIBUTION_MONEY_STATUS_UNDONE = 0;
    const DISTRIBUTION_MONEY_STATUS_PART = 10;
    const DISTRIBUTION_MONEY_STATUS_DONE = 20;
    const DISTRIBUTION_MONEY_STATUS_ERROR = 99;
    const REFUND_STATUS_NO = 0;
    const REFUND_STATUS_PART = 1;
    const REFUND_STATUS_ALL = 2;

    /**
     * @notes 关联OrderGoods模型
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public function orderGoods()
    {
        return $this->hasMany('order_goods', 'order_id', 'id')
            ->field('id,order_id,goods_id,item_id,goods_name,goods_price,spec_value,image,goods_num,is_comment,refund_status,commission_ratio,total_pay_price,bar_code,after_sale_id,discount_price,send_num,sync_item_id');
    }

    /**
     * @notes 订单用户
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id')
            ->field('id,sn,nickname,avatar,level,mobile,sex,create_time');
    }

    /**
     * @notes 关联Shop模型
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function shop()
    {
        return $this->hasOne(Shop::class, 'id', 'shop_id')
            ->field('id,name,type,logo,mobile,openid,open_invoice,spec_invoice,sync_type,sync_shop_id,sync_access_token,sync_access_token_expire_time,sync_app_key,sync_app_secret,sync_refresh_token')
            ->append(['type_desc']);
    }

    /**
     * @notes 关联发票模型
     * @return \think\model\relation\HasOne
     * <AUTHOR>
     * @date 2022/4/11 18:39
     */
    public function invoice()
    {
        return $this->hasOne(OrderInvoice::class, 'order_id', 'id');
    }

    /**
     * @Notes: 订单发货状态
     * @param bool $type
     * @return array|mixed|string
     */
    public static function getShippingStatus($type=true)
    {
        $desc = [
            OrderEnum::SHIPPING_NO => '待发货',   //未发货
            OrderEnum::SHIPPING_FINISH => '已发货',  //已发货
            OrderEnum::SHIPPING_PART => '部分发货',  //部分发货
        ];

        if ($type === true) {
            return $desc;
        }
        return $desc[$type] ?? '未知来源';
    }

    /**
     * @notes 订单状态
     * @param bool $status
     * @param int $shop_id
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:46 下午
     */
    public static function getOrderStatus($status = true, $shop_id = 0)
    {

        $desc = [
            OrderEnum::ORDER_STATUS_NO_PAID => "待付款",
            OrderEnum::ORDER_STATUS_DELIVERY => "待发货",
            OrderEnum::ORDER_STATUS_GOODS => "待收货",
            OrderEnum::ORDER_STATUS_COMPLETE => "已完成",
            OrderEnum::ORDER_STATUS_DOWN => "已关闭",
        ];
        if ($status === true) {
            return $desc;
        }
        return $desc[$status] ?? '未知';
    }

    /**
     * 订单商品表ls_order_goods.refund_status对应的文本
     * @param $status
     * @return string|string[]
     */
    public static function getGoodsRefundStatus($status = true)
    {
        $desc = [
            OrderEnum::GOODS_REFUND_STATUS_NO_REFUND => "未退款",
            OrderEnum::GOODS_REFUND_STATUS_APPLY_REFUND => "申请退款",
            OrderEnum::GOODS_REFUND_STATUS_WAIT_REFUND => "等待退款",
            OrderEnum::GOODS_REFUND_STATUS_REFUNDED => "退款成功",
        ];
        if ($status === true) {
            return $desc;
        }
        return $desc[$status] ?? '未知';
    }

    /**
     * @notes 订单类型
     * @param $type
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public static function getOrderType($type)
    {

        $desc = [
            OrderEnum::NORMAL_ORDER => '普通订单',
            OrderEnum::SECKILL_ORDER => '秒杀订单',
            OrderEnum::TEAM_ORDER => '拼团订单',
            OrderEnum::BARGAIN_ORDER => '砍价订单',
        ];

        if ($type === true) {
            return $desc;
        }
        return $desc[$type] ?? '未知';
    }

    /**
     * @notes 配送方式
     * @param $type
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public static function getDeliveryType($type)
    {
        $desc = [
            OrderEnum::DELIVERY_TYPE_EXPRESS => '快递发货',
            OrderEnum::DELIVERY_TYPE_VIRTUAL => '虚拟发货',
            OrderEnum::DELIVERY_TYPE_SELF => '线下自提',
        ];

        if ($type === true) {
            return $desc;
        }
        return $desc[$type] ?? '未知';
    }

    /**
     * @notes 配送方式
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public function getDeliveryTypeTextAttr($value, $data)
    {

        return self::getDeliveryType($data['delivery_type']);
    }

    /**
     * @notes 订单类型
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public function getOrderTypeTextAttr($value, $data)
    {

        return self::getOrderType($data['order_type']);
    }

    /**
     * @notes 订单状态
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:47 下午
     */
    public function getOrderStatusTextAttr($value, $data)
    {

        return self::getOrderStatus($data['order_status']);
    }

    /**
     * @notes 订单支付方式
     * @param $value
     * @param $data
     * @return array|mixed|string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getPayWayTextAttr($value, $data)
    {

        return PayEnum::getPayWay($data['pay_way']);
    }

    /**
     * @notes 订单支付状态
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getPayStatusTextAttr($value, $data)
    {

        return PayEnum::getPayStatus($data['pay_status']);
    }

    /**
     * @notes 订单来源
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getOrderSourceTextAttr($value, $data)
    {

        return Client_::getClient($data['order_source']);
    }

    /**
     * @notes 订单商品数量
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getGoodsCountAttr($value, $data)
    {

        return count($this->order_goods);
    }

    /**
     * Notes:
     * Author: Darren
     * DateTime: 2023-05-10 19:06
     * @param $value
     * @param $data
     * @return string
     */
    public function getAddressArrayAttr($value, $data)
    {
        $region = Db::name('dev_region')
            ->where('id', 'IN', [$data['province'], $data['city'], $data['district']])
            ->order('level asc')
            ->column('name');
        return $region;
    }

    public function distribution()
    {
        return $this->hasOne(Distribution::class, 'id', 'distribution_id')
            ->field('id,user_id,distribution_agent_name,real_name,mobile,business_card');
    }

    /**
     * @notes 收货地址
     * @param $value
     * @param $data
     * @return string
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getDeliveryAddressAttr($value, $data)
    {

        $region = Db::name('dev_region')
            ->where('id', 'IN', [$data['province'], $data['city'], $data['district']])
            ->order('level asc')
            ->column('name');

        $region_desc = implode('', $region);
        return $region_desc . $data['address'];
    }

    public function getPcAddressAttr($value, $data)
    {

        $region = Db::name('dev_region')
            ->where('id', 'IN', [$data['province'], $data['city'], $data['district']])
            ->order('level asc')
            ->column('name');

        $region_desc = implode(' ', $region);
        return $region_desc . $data['address'];
    }


    /**
     * @notes 返回是否显示支付按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getPayBtnAttr($value, $data)
    {

        $btn = 0;
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_NO_PAID && $data['pay_status'] == PayEnum::UNPAID) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * @notes 返回是否显示取消订单按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:48 下午
     */
    public function getCancelBtnAttr($value, $data)
    {

        $btn = 0;
        if (is_string($data['create_time'])) {
            $data['create_time'] = strtotime($data['create_time']);
        }
        //多长时间内允许客户自动取消
        $cancel_limit = ConfigServer::get('transaction', 'paid_order_cancel_time', 0);
        if (!$cancel_limit || $cancel_limit<0){
            return 1;
        }
        $limit_time = $data['create_time'] + $cancel_limit * 60;
        if ($limit_time < time()) {
            return $btn;
        }

        if (($data['order_status'] == OrderEnum::ORDER_STATUS_NO_PAID && $data['pay_status'] == PayEnum::UNPAID)
            || ($data['pay_status'] == PayEnum::ISPAID && $data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY && $data['shipping_status'] == OrderEnum::SHIPPING_NO)) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * Notes: 发货单列表按钮
     * Author: Darren
     * DateTime: 2024-11-19 16:58
     * @param $value
     * @param $data
     * @return int
     */
    public function getDeliveryListBtnAttr($value, $data)
    {

        $btn = 0;
        // 虚拟发货类型不显示物流查询按钮
        if ($data['delivery_type'] != OrderEnum::DELIVERY_TYPE_EXPRESS) {
            return $btn;
        }
        if ($data['is_part_shipping'] != 1){
            return $btn;
        }
        //部分发货了
        if ($data['is_part_shipping'] == 1){
            $btn = 1;
        }

        if ($data['order_status'] == OrderEnum::ORDER_STATUS_GOODS && $data['pay_status'] == PayEnum::ISPAID && ($data['shipping_status'] == OrderEnum::SHIPPING_FINISH || $data['shipping_status'] == OrderEnum::SHIPPING_PART)) {
            $btn = 1;
        }
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE && $data['pay_status'] == PayEnum::ISPAID && ($data['shipping_status'] == OrderEnum::SHIPPING_FINISH || $data['shipping_status'] == OrderEnum::SHIPPING_PART)) {
            $btn = 1;
        }
        return $btn;

    }

    /**
     * @notes 返回是否显示物流按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getDeliveryBtnAttr($value, $data)
    {

        $btn = 0;
        // 虚拟发货类型不显示物流查询按钮
        if ($data['delivery_type'] != OrderEnum::DELIVERY_TYPE_EXPRESS) {
            return $btn;
        }
        //拆分发货的不显示物流查询按钮
        if ($data['is_part_shipping']){
            return $btn;
        }

        if ($data['order_status'] == OrderEnum::ORDER_STATUS_GOODS && $data['pay_status'] == PayEnum::ISPAID && $data['shipping_status'] == 1) {
            $btn = 1;
        }
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE && $data['pay_status'] == PayEnum::ISPAID && $data['shipping_status'] == 1) {
            $btn = 1;
        }
        return $btn;

    }

    /**
     * @notes 返回是否显示确认收货按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getTakeBtnAttr($value, $data)
    {
        $btn = 0;
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_GOODS
            && $data['pay_status'] == PayEnum::ISPAID
            && $data['shipping_status'] == 1
            && $data['delivery_type'] != OrderEnum::DELIVERY_TYPE_SELF
        ) {
            $btn = 1;
        }
        return $btn;
    }

//    /**
//     * @notes 返回是否显示申请售后按钮-------改在每款商品中判断
//     * @param $value
//     * @param $data
//     * @return int
//     * <AUTHOR>
//     * @date 2021/7/13 6:49 下午
//     */
//    public function getAfterApplyBtnAttr($value, $data)
//    {
//        $btn = 0;
//        //已经发货, 已经发货
//        if ($data['shipping_status'] == 1 && $data['pay_status'] == PayEnum::ISPAID){
//            //尚未评论 可以申请售后
//            if (isset($data['is_comment']) && $data['is_comment'] != 1) {
//                $btn = 1;
//            }
//        }
//        return $btn;
//    }

    /**
     * @notes 返回是否显示删除按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getDelBtnAttr($value, $data)
    {

        $btn = 0;
        if (isset($data['del']) && $data['del']){
            return $btn;
        }

        if (
            ($data['order_status'] == OrderEnum::ORDER_STATUS_DOWN && $data['pay_status'] == PayEnum::UNPAID) ||
            ($data['order_status'] == OrderEnum::ORDER_STATUS_DOWN && $data['pay_status'] == PayEnum::REFUNDED)
        ) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * @notes 返回是否显示已完成按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getFinishBtnAttr($value, $data)
    {

        $btn = 0;
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE && $data['pay_status'] == PayEnum::ISPAID && $data['delivery_type'] != OrderEnum::DELIVERY_TYPE_SELF) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * @notes 返回是否显示去评论按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getCommentBtnAttr($value, $data)
    {

        $btn = 0;
        $comment_count = 0;
        //有售后的 不能评价
        $after_sale = AfterSale::where(['order_id' => $data['id']])->find();
        if ($after_sale){
            return 0;
        }
        if ($data['pay_status'] == PayEnum::ISPAID && $data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE) {
            $btn = 1;
            foreach ($this->order_goods as $item) {
                if ($item['is_comment'] == 1) {
                    $comment_count += 1;
                };
            }
            if (count($this->orderGoods) == $comment_count) {
                $btn = 0;
            }
        }

        return $btn;
    }

    /**
     * @notes 返回是否显示申请退款按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getRefundBtnAttr($value, $data)
    {

        $btn = 0;
        $data['confirm_take_time'] = strtotime($data['confirm_take_time']);
        $refund_days = ConfigServer::get('after_sale', 'refund_days', '', 0);
        if (!$refund_days){
            return 1;
        }
        $refund_days_time = $data['confirm_take_time'] + $refund_days * 86400;
        $now = time();

        //订单已完成、在售后期内。未申请退款、
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE && $refund_days_time > $now && $data['refund_status'] = OrderGoodsEnum::REFUND_STATUS_NO) {
            $btn = 1;
        }
        return $btn;
    }


    /**
     * @notes 发货内容(查看内容按钮)
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2022/4/8 10:22
     */
    public function getContentBtnAttr($value, $data)
    {
        $btn = 0;
        if ($data['delivery_type'] == OrderEnum::DELIVERY_TYPE_VIRTUAL && $data['shipping_status'] == OrderEnum::SHIPPING_FINISH) {
            $btn = 1;
        }
        return $btn;
    }


    /**
     * @notes 申请开票按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2022/4/12 15:19
     */
    public function getSaveInvoiceBtnAttr($value, $data)
    {
        $btn = 0;
        $invoice = OrderInvoice::where(['order_id' => $data['id']])->findOrEmpty();
        if ($invoice->isEmpty()) {
            $btn = 1;
        }
        return $btn;
    }
    

    /**
     * @notes 查看开票按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2022/4/12 15:19
     */
    public function getViewInvoiceBtnAttr($value, $data)
    {
        $btn = 0;
        $invoice = OrderInvoice::where(['order_id' => $data['id']])->findOrEmpty();
        if (!$invoice->isEmpty()) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * @notes 取消订单时间
     * @param $value
     * @param $data
     * @return false|float|int|string
     * <AUTHOR>
     * @date 2021/7/13 6:49 下午
     */
    public function getOrderCancelTimeAttr($value, $data)
    {

        $end_time = '';
        if (is_string($data['create_time'])) {
            $data['create_time'] = strtotime($data['create_time']);
        }
        if ($data['order_status'] == 0 && $data['pay_status'] == 0) {
            $order_cancel_time = ConfigServer::get('transaction', 'unpaid_order_cancel_time', 30);
            $end_time = $data['create_time'] + $order_cancel_time * 60;
        }
        return $end_time;
    }

    /**
     * @notes 关联未评价子订单
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     * @date 2021/7/13 6:50 下午
     */
    public function orderGoodsUnComment()
    {

        return $this->hasMany('order_goods', 'order_id', 'id')
            ->field('id,order_id,goods_id,item_id,goods_num,goods_name,goods_price,is_comment')
            ->where('is_comment', 0);
    }

    /**
     * @notes 返回是否显示修改地址按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/11/10 11:37 上午
     */
    public function getEditAddressBtnAttr($value, $data)
    {

        $btn = 0;

        if ($data['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF) {
            return $btn;
        }
        //部分发货了也不允许再次修改
        if ($data['is_part_shipping'] == 1) {
            return $btn;
        }
        //付款之后不能再修改地址
        if ($data['pay_status'] == PayEnum::ISPAID) {
            return $btn;
        }
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_NO_PAID) {
            $btn = 1;
        }

        return $btn;

    }


    /**
     * 再次购买
     * @param $value
     * @param $data
     * @return int
     */
    public function getReBuyBtnAttr($value, $data)
    {
        $btn = 0;
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY || $data['order_status'] == OrderEnum::ORDER_STATUS_COMPLETE || $data['order_status'] == OrderEnum::ORDER_STATUS_DOWN) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * 催发货-已支付-待发货状态
     * @param $value
     * @param $data
     * @return int
     */
    public function getUrgeSendBtnAttr($value, $data)
    {
        $btn = 0;
        //完成催发货逻辑后开放
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY && $data['pay_status'] == PayEnum::ISPAID && $data['delivery_type'] == OrderEnum::DELIVERY_TYPE_EXPRESS) {
            $btn = 1;
        }
        return $btn;
    }

    /**
     * @notes 返回是否显示去发货按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/11/10 11:41 上午
     */
    public function getToShipBtnAttr($value, $data)
    {

        $btn = 0;
        if ($data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY && $data['pay_status'] == PayEnum::ISPAID && $data['delivery_type'] != OrderEnum::DELIVERY_TYPE_SELF) {
            $btn = 1;
        }
        return $btn;

    }

    /**
     * @notes 返回是否显示取消订单(商家端)按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2021/11/10 11:41 上午
     */
    public function getShopCancelBtnAttr($value, $data)
    {
        $btn = 0;
        //部分发货了也不允许再次修改
        if ($data['is_part_shipping'] == 1) {
            return $btn;
        }
        if (($data['order_status'] == OrderEnum::ORDER_STATUS_NO_PAID && $data['pay_status'] == PayEnum::UNPAID)
            || ($data['pay_status'] == PayEnum::ISPAID && $data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY)) {
            $btn = 1;
        }
        return $btn;
    }


    /**
     * @notes 返回是否显示跳转核销(商家端)按钮
     * @param $value
     * @param $data
     * @return int
     * <AUTHOR>
     * @date 2022/11/03 18:57
     */
    public function getToVerificationBtnAttr($value, $data)
    {
        $btn = 0;
        if ($data['pay_status'] == PayEnum::ISPAID
            && $data['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY
            && $data['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF
            && $data['verification_status'] == OrderEnum::NOT_WRITTEN_OFF
        ) {
            $btn = 1;
        }
        return $btn;
    }


    /**
     * @notes 核销状态描述
     * @param $value
     * @param $data
     * @return string|string[]
     * <AUTHOR>
     * @date 2022/11/2 14:16
     */
    public function getVerificationStatusTextAttr($value, $data)
    {
        return OrderEnum::getVerificationStatusDesc($data['verification_status']);
    }

    /**
     * Notes: 与聚水潭字段对应
     * Author: Darren
     * DateTime: 2023-05-10 11:29
     */
    public static function jushuitanFiledsMatch($order_id){
        try {
            $order_info = Order::where('id', $order_id)
                ->with(['user','shop', 'orderGoods'])
                ->append(['address_array'])
                ->find()
                ->toArray();
            if (!$order_info || !isset($order_info['shop'])){
                throw new \Exception('订单或订单商品不存在');
            }
            if (!$order_info['pay_time']){
                throw new \Exception('订单未支付不推送');
            }
            JushuitanLogic::shopconfig($order_info['shop_id'], $order_info['shop']);
            /**
             * 等待买家付款=WAIT_BUYER_PAY，
             * 等待卖家发货=WAIT_SELLER_SEND_GOODS（传此状态时实际支付金额即pay节点支付金额=应付金额ERP才会显示已付款待审核）,
             * 等待买家确认收货=WAIT_BUYER_CONFIRM_GOODS,
             * 交易成功=TRADE_FINISHED,
             * 付款后交易关闭=TRADE_CLOSED,
             * 付款前交易关闭=TRADE_CLOSED_BY_TAOBAO；可更新
             */
            $shop_status_arr[OrderEnum::ORDER_STATUS_NO_PAID] = 'WAIT_BUYER_PAY';
            $shop_status_arr[OrderEnum::ORDER_STATUS_DELIVERY] = 'WAIT_SELLER_SEND_GOODS';
            $shop_status_arr[OrderEnum::ORDER_STATUS_GOODS] = 'WAIT_BUYER_CONFIRM_GOODS';
            $shop_status_arr[OrderEnum::ORDER_STATUS_COMPLETE] = 'TRADE_FINISHED';
            $shop_status_arr[OrderEnum::ORDER_STATUS_DOWN] = 'TRADE_CLOSED';
//            $item_id_arr = array_column($order_info['orderGoods'], 'item_id');
//            $item_code_list = GoodsItem::where([['id', 'in', $item_id_arr]])->column('bar_code,sync_item_id', 'id');
            //组织订单商品数据
            foreach ($order_info['orderGoods'] as $k=>$goods){
                $bar_code = isset($goods['bar_code']) ? strval($goods['bar_code']) : '';
                $shop_sku_id = isset($goods['sync_item_id']) ? strval($goods['sync_item_id']) : '';
                $ju_order_goods[$k]=[
                    'sku_id'=>$bar_code, //ERP中商品编码, 对与普通商品 sku_id, 从ERP中复制过来
                    'shop_sku_id'=>$shop_sku_id, //商城店铺商品规格编码
                    'amount'=>money($goods['total_pay_price']),
                    'base_price'=>money($goods['goods_price']),
                    'qty'=>$goods['goods_num'],
                    'name'=>$goods['goods_name'],
                    'outer_oi_id'=>strval($goods['id']),    //商家系统订单商品明细主键,为了拆单合单时溯源,保持订单内唯一，支持自定义
                    //非必填字段
                    'properties_value'=>$goods['spec_value'],//规格属性
                    'pic'=>$goods['image'],
                    //'refund_status'=>$goods['refund_status'],//值存在，会自动将订单转异常,success状态的，发货将不发该商品，不支持单商品退部分;退款状态:可选退款中=waiting;退款成功=success（订单的全部商品传此状态，订单会被取消）,closed=退款关闭；可更新
                ];
            }
            //组织订单数据
            $ju_order=[
                'shop_id'=>$order_info['shop']['sync_shop_id'],
                'so_id'=>$order_info['order_sn'],
                'order_date'=>$order_info['create_time'],
                'shop_status'=>$shop_status_arr[$order_info['order_status']],
                'shop_buyer_id'=>$order_info['user']['sn'],
                'receiver_state'=>$order_info['address_array'][0],
                'receiver_city'=>$order_info['address_array'][1],
                'receiver_district'=>$order_info['address_array'][2],
                'receiver_address'=>$order_info['address'],
                'receiver_name'=>$order_info['consignee'],
                'receiver_phone'=>$order_info['mobile'],
                'pay_amount'=>money($order_info['order_amount']),//应付金额
                'freight'=>money($order_info['shipping_price']),//运费
                'items'=>$ju_order_goods,
                //非必填字段
                'buyer_message'=>$order_info['user_remark'],
                'shop_modified'=>$order_info['update_time'],
                'remark'=>$order_info['order_remarks'],//订单备注
            ];
            //支付信息
            if ($order_info['order_status'] != OrderEnum::ORDER_STATUS_NO_PAID){
                $pay =[
                    'outer_pay_id' =>$order_info['order_sn'],
                    'pay_date' =>date('Y-m-d H:i:s', $order_info['pay_time']),
                    'payment' =>PayEnum::getPayWay($order_info['pay_way']),
                    'seller_account' =>strval($order_info['shop_id']),//卖家支付账号 todo 确定使用id 还是具体账号 ???
                    'buyer_account' =>strval($order_info['user_id']),//买家支付账号 todo 确定使用id 还是具体账号 ???
                    'amount' =>money($order_info['order_amount']),
                ];
                $ju_order['pay'] = $pay;
            }
            return $ju_order;
        } catch (Exception $e) {
            Log::write('订单同步聚水潭失败:'.$e->getMessage());
            return ['error'=>$e->getMessage()];
        }
    }

    /**
     * 格式化订单 发货后 通知微信
     * @return void
     */
    public static function formatOrderSendNoticeWechat($order_info){
        if (!$order_info){
            return [];
        }
        $order_info['user_openid_info'] = UserAuth::where(['user_id'=>$order_info['user_id']])->value('openid');
        if (!$order_info['user_openid_info']){
            return [];
        }
        $order_goods_str = '';
        if ($order_info['order_goods']) {
            foreach ($order_info['order_goods'] as $goods) {
                $order_goods_str .= $goods['goods_name'].' * '.$goods['goods_num'].'；';
            }
        }
        $order_goods_str = mb_substr($order_goods_str, 0, 100).'等';
        $transaction_id = $order_info['transaction_id'] ?: '';
        if (!$transaction_id){
            $transaction_id = OrderTrade::where(['order_id'=>$order_info['id']])->value('transaction_id');
        }
        $create_time = date(DATE_RFC3339, time());
        $shipping_code = Express::where(['id'=>$order_info['delivery_info']['shipping_id']])->value('code');
        $order_result = [
            'order_key'=>[
                'order_number_type'=>2, //订单号类型，枚举值：1、订单号 2 微信支付单号。
                'transaction_id'=> $transaction_id //微信支付订单号
            ],
            'logistics_type'=>1, //物流模式，发货方式枚举值：1、实体物流配送
            'delivery_mode'=>1,//发货模式枚举值：1、UNIFIED_DELIVERY（统一发货）2、SPLIT_DELIVERY（分拆发货）
            'is_all_delivered'=>true,
            'shipping_list'=>[
                [
                    'tracking_no'=>$order_info['delivery_info']['invoice_no'] ?? '',//物流单号
                    'express_company'=>$shipping_code ?? 'ZTO',//物流公司
                    'item_desc'=> $order_goods_str ?? '商品列表',//物流备注
                    'contact'=>[
                        'receiver_contact'=>$order_info['delivery_info']['mobile']
                    ],
                ]
            ],
            'upload_time'=> $create_time,
            'payer'=>[
                'openid'=> $order_info['user_openid_info']
            ]
        ];
        return $order_result;
    }
}