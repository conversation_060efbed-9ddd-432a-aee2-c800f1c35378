<?php


namespace app\common\command;


use app\admin\logic\distribution\DistributionLevelLogic;
use app\common\enum\DistributionEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\model\AccountLog;
use app\common\model\after_sale\AfterSale;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class DistributionOrder extends Command
{
    protected function configure()
    {
        $this->setName('distribution_order')
            ->setDescription('结算分销订单');
    }

    protected function execute(Input $input, Output $output)
    {
        echo 'start--DistributionOrder--'.PHP_EOL;
        $distribution_settlement_type = ConfigServer::get('transaction', 'distribution_settlement_type', '');
        if ($distribution_settlement_type != 1){
            echo '订单结算方式不对:distribution_settlement_type--'.$distribution_settlement_type;
            return false;
        }
        Db::startTrans();
        try {
            // 1、获取结算时间
            $time = time();
            $afterSaleTime = ConfigServer::get('distribution', 'settlement_days', 7);
            $afterSaleTime = intval($afterSaleTime * 24 * 60 * 60);
            // 2、查询可以结算的订单
            $model = new DistributionOrderGoods();
            $where=[
                ['comment_time', 'between', [1, $afterSaleTime + $time]],
                ['status', '=', DistributionOrderGoodsEnum::STATUS_WAIT_HANDLE],
                ['level', 'in', [DistributionEnum::DISTRIBUTION_LEVEL_FIRST,DistributionEnum::DISTRIBUTION_LEVEL_SECOND]],
            ];
            $orders = $model->where($where)
                ->limit(100)
                ->select()
                ->toArray();
            if (!$orders){
                echo ' distribution_order_count:0';
                Db::commit();
                return true;
            }
            echo ' distribution_order_count:'.count($orders);
            foreach ($orders as &$order) {
                //当前分佣订单是否可结算
                if (false === self::isSettle($order)) {
                    continue;
                }
                $order_info = Order::where('id', $order['order_id'])->field('order_sn,transaction_id,pay_way,hf_seq_id')->find();
                $user_info = User::where('id', $order['user_id'])->field('id,earnings,partner_earnings')->find();
                // 增加用户佣金
                if ($order_info['pay_way'] == PayEnum::HFDG_WECHAT || $order_info['pay_way'] == PayEnum::HFDG_ALIPAY){  //汇付支付的通过其他任务处理
                    continue;
                }else{
                    $user_info['earnings'] = money($user_info['earnings'] + $order['money']);
                }
                $user_info['update_time'] = $time;
                $user_info->save();

                // 记录流水
                AccountLog::create([
                    'log_sn'        => createSn('account_log', 'log_sn', '', 4),
                    'user_id'       => $order['user_id'],
                    'source_type'   => AccountLog::distribution_inc_earnings,
                    'source_id'     => $order['order_id'],
                    'source_sn'     =>$order_info['order_sn'],
                    'change_amount' => $order['money'],
                    'left_amount'   => money($user_info['earnings'] + $user_info['partner_earnings']),
                    'change_type'   => 1,
                    'remark'        => '分销佣金增加',
                    'transaction_id'     =>$order_info['transaction_id'],
                    'pay_way'     =>$order_info['pay_way'],
                ]);
                // 更新分销订单状态
                DistributionOrderGoods::update([
                    'status'      => DistributionOrderGoodsEnum::STATUS_SUCCESS,
                    'update_time' => $time,
                    'settlement_time' => $time
                ],['id'=>$order['id']]);

                // 更新订单分销佣金
                $orderModel = Order::findOrEmpty($order['order_id']);
                $orderModel->distribution_money = $orderModel->distribution_money + $order['money'];
                $orderModel->update_time = $time;
                $orderModel->save();
                // 更新分销会员等级
//                DistributionLevelLogic::updateDistributionLevel($order['user_id']);

            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('分销结算异常:'.$e->getMessage());
            return false;
        }
    }

    /**
     * @Notes: 是否可以结算分佣订单 (检查是否有售后记录 没有则可结算, 有则需要检查售后记录状态)
     * @Author: 张无忌
     * @param $order
     * @return bool
     */
    protected function isSettle($order)
    {
        // 订单是否在售后(正在退款或已退款)
        $check = (new AfterSale())->where([
            'order_id'       => $order['order_id'],
            'order_goods_id' => $order['order_goods_id'],
            'del'=>0
        ])->findOrEmpty()->toArray();

        if (!$check) {
            return true;
        }

        // 有售后订单记录且状态 $no_settlement中的 不结算分佣订单
        $no_settlement = [
            AfterSale::STATUS_APPLY_REFUND,       //申请退款
            AfterSale::STATUS_WAIT_RETURN_GOODS,  //商品待退货
            AfterSale::STATUS_WAIT_RECEIVE_GOODS, //商家待收货
        ];

        // 不结算且分佣订单改为已失效
        $set_fail = [
            AfterSale::STATUS_WAIT_REFUND,    //等待退款
            AfterSale::STATUS_SUCCESS_REFUND, //退款成功
        ];

        // 售后情况不明 不结算
        if (in_array($check['status'], $no_settlement)) {
            return false;
        }

        // 分佣订单更新为已失效  不结算
        if (in_array($check['status'], $set_fail)) {
            DistributionOrderGoods::update([
                'status'      => DistributionOrderGoodsEnum::STATUS_ERROR,
                'update_time' => time()
            ], ['order_goods_id'=>$check['order_goods_id']]);

            return false;
        }

        return true;
    }
}