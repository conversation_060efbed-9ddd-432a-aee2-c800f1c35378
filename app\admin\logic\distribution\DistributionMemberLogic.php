<?php
namespace app\admin\logic\distribution;

use app\common\basics\Logic;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\common\enum\DistributionLevel as LevelEnum;

/**
 * 分销会员逻辑层
 * Class DistributionMemberLogic
 * @package app\admin\logic\distribution
 */
class DistributionMemberLogic extends Logic
{
    /**
     * @notes 分销会员列表
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2021/9/2 18:44
     */
    public static function lists($params)
    {
        $where = [
            ['d.is_distribution', '=', 1]
        ];
        // 用户信息
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['u.sn|u.nickname|u.mobile|u.real_name', 'like', '%'. $params['keyword'] .'%'];
        }
        // 分销等级
        if (isset($params['level_id']) && $params['level_id'] != 'all') {
            $where[] = ['d.level_id', '=', $params['level_id']];
        }
        // 分销状态
        if (isset($params['is_freeze']) && $params['is_freeze'] != 'all') {
            $where[] = ['d.is_freeze', '=', $params['is_freeze']];
        }
        if (isset($params['first_leader']) && $params['first_leader']) {
            $where[] = ['d.user_id', '<>', $params['first_leader']];//自己的信息不在显示
            $where[] = ['d.first_leader', '=', $params['first_leader']];
        }
        if (isset($params['distribution_keyword']) && $params['distribution_keyword'] != '') {
            $where_distribution = [['distribution_agent_name|real_name|mobile', 'like', '%'. $params['distribution_keyword'] .'%']];
            $distribution_leader = Distribution::where($where_distribution)->find();
            if ($distribution_leader && isset($distribution_leader['id'])){
                $where[] = ['d.first_leader', '=', $distribution_leader['id']];
            }
        }
        $join_dog_info = 'dog.user_id = d.user_id and  dog.level = 1';
//        if (isset($params['quick_date']) && !empty($params['quick_date'])) {
//            if ($params['quick_date'] == 'today'){
//                $start_time = strtotime('today 00:00:00');
//                $end_time = strtotime('today 23:59:59');
//            }elseif($params['quick_date'] == 'yesterday'){
//                $start_time = strtotime('yesterday 00:00:00');
//                $end_time = strtotime('yesterday 23:59:59');
//            }elseif($params['quick_date'] == 'this_month'){
//                $start_time = strtotime(date('Y-m-01 00:00:00'));
//                $end_time = strtotime(date('Y-m-01 00:00:00', strtotime('+1 month')) . ' -1 second');
//            }elseif($params['quick_date'] == 'last_month'){
//                $start_time = strtotime('first day of last month 00:00:00');
//                $end_time = strtotime('last day of last month 23:59:59');
//            }elseif($params['quick_date'] == 'all'){
//                $start_time = 0;
//                $end_time = 0;
//            }
//            if ($start_time && $end_time){
//                $join_dog_info .= ' and dog.create_time between ' . $start_time . ' and ' . $end_time;
////                $where[] = ['dog.create_time', 'between', [$start_time, $end_time]];
//            }
//        }else{
//            $start_time = isset($params['start_time']) && $params['start_time'] ? strtotime($params['start_time']) : 0;
//            $end_time = isset($params['end_time']) && $params['end_time'] ? strtotime($params['end_time']) : 0;
//            if ($start_time && $end_time) {
////              $where[] = ['dog.create_time', 'between', [$start_time, $end_time]];
//                $join_dog_info .= ' and dog.create_time between ' . $start_time . ' and ' . $end_time;
//            }else if($start_time){
////                $where[] = ['dog.create_time', '>=', $start_time];
//                $join_dog_info .= ' and dog.create_time >= ' . $start_time;
//            }else if($end_time){
////                $where[] = ['dog.create_time', '<=', $end_time];
//                $join_dog_info .= ' and dog.create_time <= ' . $end_time;
//            }
//        }
        $sortField = $params['sortField'] ?? 'd.id';
        $sortOrder = $params['sortOrder'] ?? 'desc';
        $field = [
            'u.id' => 'user_id',
            'u.sn' => 'user_sn',
            'u.avatar',
            'u.nickname',
            'u.mobile',
            'u.first_leader',
            'u.second_leader',
            'u.origin_leader',
            'u.earnest_money',
            'sum(dog.total_price) as sum_total_price',
            'sum(dog.money) as sum_money',
            'd.is_freeze',
            'd.id',
            'd.level_id',
            'd.mobile',
            'd.real_name',
            'd.business_card',
            'd.distribution_time',
            'd.distribution_agent_name',
            'd.pay_partner_account_code'
        ];
        $lists = Distribution::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
            ->leftJoin('distribution_order_goods dog', $join_dog_info)
            ->field($field)
            ->where($where)
            ->group('d.user_id')
            ->order($sortField, $sortOrder)
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();
        $count = Distribution::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
            ->leftJoin('distribution_order_goods dog', $join_dog_info)
            ->field('COUNT(DISTINCT d.user_id) as count')
            ->where($where)
            ->find();
        $count = $count['count'] ?? 0;
        $levels = DistributionLevel::field('id,name')->column('name', 'id');
//        if (!isset($distribution_leader['id'])){
//            $distribution_leader = Distribution::where('user_id', $params['user_id'])->findOrEmpty()->toArray();
//        }
        foreach($lists as &$item) {
            $item['avatar'] = empty($item['avatar']) ? '' : UrlServer::getFileUrl($item['avatar']);
//            $item['earnings'] = DistributionOrderGoods::getEarnings($item['user_id']);
            $item['level_name'] = $levels[$item['level_id']] ?? '';
            $item['sum_total_price'] = money($item['sum_total_price']);
            $item['sum_money'] = money($item['sum_money']);
            $item['first_leader_info'] = User::getUserInfo($item['first_leader']);
            if ($item['first_leader_info']){
                $first_leader_info =  Distribution::field('distribution_agent_name, real_name, mobile')->where('user_id', $item['first_leader'])->find();
                $item['first_leader_info']['distribution_agent_name'] = $first_leader_info['distribution_agent_name'] ?? '';
                $item['first_leader_info']['distribution_real_name'] = $first_leader_info['real_name'] ?? '';
            }
//            $item['second_leader_info'] = User::getUserInfo($item['second_leader']);
        }
        return [
            'count' => $count,
            'lists' => $lists
        ];
    }

    /**
     * @notes 用户列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/3 9:55
     */
    public static function getUserLists($params)
    {
        $where[] = ['del', '=', 0];
        // 用户信息
        if (isset($params['keyword']) && !empty($params['keyword'])) {
            $where[] = ['sn|nickname|mobile', 'like', '%'. $params['keyword'] .'%'];
        }

        $lists = User::field('id,sn,mobile,nickname,id as distribution')
            ->where($where)
            ->withSearch(['distribution'], $params)
            ->page($params['page'], $params['limit'])
            ->select()
            ->toArray();
        $count = User::where($where)->withSearch(['distribution'], $params)->count();

        return [
            'count' => $count,
            'lists' => $lists,
        ];
    }

    /**
     * @notes 开通分销会员
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 11:09
     */
    public  static function open($params)
    {
        try {
            $user = User::where('id', $params['user_id'])->findOrEmpty()->toArray();
            if(empty($user)) {
                throw new \Exception('用户不存在');
            }
            $distribution = Distribution::where('user_id', $params['user_id'])->findOrEmpty()->toArray();
            if(!empty($distribution) && $distribution['is_distribution'] == 1) {
                throw new \Exception('用户已是分销会员');
            }
            if(!empty($distribution) && $distribution['is_distribution'] == 0) {
                Distribution::where('user_id', $params['user_id'])->update([
                    'is_distribution' => 1,
                    'distribution_time' => time()
                ]);
            }
            if(empty($distribution)) {
                $data = [
                    'user_id' => $params['user_id'],
                    'mobile' => $user['mobile'],
                    'level_id' => $params['level_id'],
                    'distribution_agent_name' => $params['distribution_agent_name'],
                    'is_distribution' => 1,
                    'is_freeze' => 0,
                    'remark' => '后台开通分销',
                    'distribution_time' => time()
                ];

                Distribution::create($data);
            }

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function getUser($params)
    {
        $field = [
            'u.id' => 'user_id',
            'u.sn' => 'user_sn',
            'u.nickname' => 'user_nickname',
            'dl.name' => 'level_name',
            'dl.weights',
            'd.distribution_agent_name',
            'd.real_name',
            'd.remark',
            'd.invite_open_shop',
            'd.distribution_commission_method',
            'd.is_freeze',
            'd.level_id',

        ];
        $info = Distribution::alias('d')
            ->leftJoin('user u', 'u.id = d.user_id')
            ->leftJoin('distribution_level dl', 'dl.id = d.level_id')
            ->field($field)
            ->where('d.user_id', $params['id'])
            ->findOrEmpty()
            ->toArray();

        return $info;
    }

    /**
     * @notes 分销会员等级调整
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 14:14
     */
    public static function adjust($params)
    {

        $oldDistribution = Distribution::where(['user_id' => $params['user_id']])->findOrEmpty();
        $new_first_leader = 0;
        //各个等级的逻辑固定
        //代理商A,店主B  按照商品分佣.
        //店长,店员,自营 按照等级分佣
        //只有代理商A和自营有邀请开店权限.
        if ($params['level_id'] == LevelEnum::LEVEL_1 || $params['level_id'] == LevelEnum::LEVEL_2) {
            $params['distribution_commission_method'] = 2;
        }else{
            $params['distribution_commission_method'] = 1;
        }
        if ($params['level_id'] == LevelEnum::LEVEL_1  || $params['level_id'] == LevelEnum::LEVEL_5) {
            $params['invite_open_shop'] = 1;
            //升级, 为代理商/自营, 有邀请开店权限的人, 上级设置为自己
            $new_first_leader = $params['user_id'];
        }
        //提交有开店权限则开通
//        else{
//            $params['invite_open_shop'] = 0;
//        }
        //任何其他等级 降级为B店主, 上级设置为默认1号店, 也就是原先的上级没有佣金了.
        if ($oldDistribution['level_id'] !== LevelEnum::LEVEL_2 && $params['level_id'] == LevelEnum::LEVEL_2) {
            $new_first_leader = ConfigServer::get('invite', 'default_distribution_id', 1);
        }
        if ($new_first_leader){
            $params['first_leader'] = $new_first_leader;
            User::where(['id' => $params['user_id']])->update(['first_leader' => $new_first_leader]);
        }else{
            $params['first_leader'] = $oldDistribution['first_leader'];
        }

        try {
            Distribution::where(['user_id' => $params['user_id']])->update([
                'level_id' => $params['level_id'],
                'distribution_agent_name' => $params['distribution_agent_name'],
                'real_name' => $params['real_name'],
                'remark' => $params['remark'],
                'first_leader' => $params['first_leader'],
                'invite_open_shop' => isset($params['invite_open_shop']) ? $params['invite_open_shop'] : 0,
                'distribution_commission_method' => isset($params['distribution_commission_method']) ? $params['distribution_commission_method'] : 1,
                'is_freeze' => isset($params['is_freeze']) ? $params['is_freeze'] : 0,
            ]);

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 冻结资格/恢复资格
     * @param $params
     * @return bool
     * <AUTHOR>
     * @date 2021/9/3 14:24
     */
    public static function isFreeze($params)
    {
        try {
            Distribution::where(['user_id' => $params['user_id']])->update([
                'is_freeze' => $params['is_freeze']
            ]);

            return true;
        } catch(\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }
}