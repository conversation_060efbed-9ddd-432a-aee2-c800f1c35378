<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------


namespace app\api\logic;


use app\common\enum\DistributionEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\NoticeEnum;
use app\common\enum\OrderEnum;
use app\common\model\AfterSale;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionGoods;
use app\common\model\distribution\DistributionLevel;
use app\common\model\distribution\DistributionLevelLog;
use app\common\model\distribution\DistributionShare;
use app\common\model\order\Order as OrderModel;
use app\common\server\ConfigServer;
use app\common\model\user\UserDistribution;
use app\common\model\user\User;
use app\common\model\distribution\DistributionMemberApply;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\server\AreaServer;
use app\common\server\HuifuPayServer;
use app\common\server\JsonServer;
use app\common\server\UrlServer;
use app\common\basics\Logic;
use app\common\logic\AccountLogLogic;
use app\common\model\AccountLog;
use think\Exception;
use think\facade\Db;

class DistributionLogic extends Logic
{
    /**
     * Notes: 根据后台设置返回当前生成用户的分销会员状态(设置了全员分销,新生成的用户即为分销会员)
     * @return int
     * <AUTHOR> 14:48)
     */
    public static function isDistributionMember()
    {
        $is_distribution = 0;
        //分销会员申请--1,申请分销; 2-全员分销;
        $distribution = ConfigServer::get('distribution', 'member_apply', 1);
        if ($distribution == 2) {
            $is_distribution = 1;
        }
        return $is_distribution;
    }

    /**
     * Desc: 生成用户扩展表
     * @param $user_id
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function createUserDistribution($user_id)
    {
        $user_distribution = UserDistribution::where(['user_id' => $user_id])->find();

        if ($user_distribution) {
            return true;
        }

        $data = [
            'user_id' => $user_id,
            'order_num' => 0,
            'order_goods_num' => 0,
            'distribution_money' => 0,
            'order_amount' => 0,
            'fans' => 0,
            'create_time' => time(),
        ];
        UserDistribution::create($data);
        return true;
    }


    /**
     * 申请分销会员
     */
    public static function apply($post)
    {
        $default_level = DistributionLevel::where('is_default', '=', 1)->find();
        $time = time();
        $data = [
            'user_id' => $post['user_id'],
            'real_name' => $post['real_name'],
            'mobile' => $post['mobile'],
            'province' => $post['province'],
            'city' => $post['city'],
            'district' => $post['district'],
            'reason' => $post['reason'] ?? '',
            'business_card' => $post['business_card'],
            'business_license_front' => $post['business_license_front'] ?? '',
            'business_license_back' => $post['business_license_back'] ?? '',
            'distribution_agent_name' => $post['distribution_agent_name'],
            'invite_code' => $post['code'],
            'level_id' => $default_level['id'] ?? 1,
            'status' => 0, // 待审核
            'create_time' => $time,
            'update_time' => $time,
        ];
        return DistributionMemberApply::create($data);
    }

    /**
     * 最新分销申请详情
     */
    public static function applyDetail($userId)
    {
        $result = DistributionMemberApply::where('user_id', $userId)
            ->order('id', 'desc')
            ->findOrEmpty();

        if ($result->isEmpty()) {
            return [];
        }
        $result = $result->toArray();
        $result['province'] = AreaServer::getAddress($result['province']);
        $result['city'] = AreaServer::getAddress($result['city']);
        $result['district'] = AreaServer::getAddress($result['district']);
        $result['business_card'] =  UrlServer::getFileUrl($result['business_card']);
        $result['business_license_front'] = UrlServer::getFileUrl($result['business_license_front']);
        $result['business_license_back'] = UrlServer::getFileUrl($result['business_license_back']);
        switch ($result['status']) {
            case 0:
                $result['status_str'] = '已提交，等待客服审核...';
                break;
            case 1:
                $result['status_str'] = '已审核通过';
                break;
            case 2:
                $result['status_str'] = '审核失败，请重新提交审核';
                break;
        }
        return $result;
    }

    /**
     * 分销主页
     */
    public static function index($userId)
    {
        /**
         * 一 店铺数据
         * 今日支付订单数 今日支付金额  今日预估收益
         * 二 团队数据
         * 今日团队销售额 今日团队预估收益
         * 三 可提现佣金
         */
        $where_myshop_today = [
            ['user_id', '=', $userId],
            ['level', '=', 1],
            ['status', '<>', DistributionOrderGoodsEnum::STATUS_ERROR]
        ];
        $myshop_today = DistributionOrderGoods::whereDay('create_time')
            ->where($where_myshop_today)
            ->field('IFNULL(sum(money), 0) as today_earnings, IFNULL(sum(total_price),0) as today_total_price, count(DISTINCT order_id) as today_order_num')
            ->find()
            ->toArray();
        // 自身及上级信息
        $user_info = self::myLeader($userId);
//        // 粉丝数(一级/二级)
//        $fans = Distribution::where([
//            ['first_leader', '=', $userId],
//            ['is_distribution', '=', 1],
//            ['is_freeze', '=', 0]
//        ])->count();
//
//        //本月预估收益(待返佣)
//        $month_earnings = DistributionOrderGoods::whereMonth('create_time')
//            ->where(['status' => 1, 'user_id' => $userId])
//            ->sum('money');
//        //累计收益(已提现)
        $history_earnings = DistributionOrderGoods::where(['status' => 5, 'user_id' => $userId])
            ->sum('money');
        // 用户分销会员等级
        $distribution = Distribution::where('user_id', $userId)->find();
        $levelName = DistributionLevel::where('id', $distribution['level_id'])->value('name');
        $distribution_level = ConfigServer::get('distribution', 'level', 2);
        if ($distribution_level == 2) {//分佣层级为2
            $team_sale_info = DistributionLogic::getTeamsaleInfo($userId);
        } else {
            $team_sale_info = [];
        }
        $month_day = date('j', time());
        $withdraw_start = ConfigServer::get('withdraw', 'withdraw_start', 25);
        $withdraw_end = ConfigServer::get('withdraw', 'withdraw_end', 30);
        if ($userId < 9){//可以提前一天测试
            $withdraw_start = $withdraw_start -1;
        }
        if($withdraw_start <= $withdraw_end){
            $is_able_withdrawal = $month_day >= $withdraw_start && $month_day <= $withdraw_end ? 1 : 0;
        }else{
            $is_able_withdrawal = $month_day <= $withdraw_end || $month_day >= $withdraw_start ? 1 : 0;
        }
        $partner_earnings = $user_info['user']['partner_earnings'] ?? 0;
        $huifu_avl_bal_total = 0;
        if ($partner_earnings>0 && $distribution['pay_partner_account_code']){
            $huifuServer = HuifuPayServer::getInstance();
            $queryHuifu =[
                'huifu_id'=>$distribution['pay_partner_account_code']
            ];
            $acctInfo = $huifuServer->userBalanceQuery($queryHuifu, 1);
            $huifu_avl_bal_total = $acctInfo['total'] ?? 0;
        }
        $data = [
            'distribution' => $distribution,
            'user' => $user_info['user'],
            'leader' => $user_info['leader'],
            'myshop_today' => $myshop_today,
            'team_sale_info' => $team_sale_info,
            'my_earnings' => [
                'total_able_withdrawal' => money($user_info['user']['earnings'] + $partner_earnings),//可提现佣金
                'able_withdrawal' => $user_info['user']['earnings'],//可提现佣金
                'history_earnings' => round($history_earnings, 2),//累计提现
                'is_able_withdrawal' => $is_able_withdrawal,//当前是否可以提现佣金
				'partner_earnings' => $partner_earnings,//完成认证佣金
                'huifu_avl_bal_total'=>$huifu_avl_bal_total //汇付可用余额
            ],
//            'fans' => $fans,
//            'month_earnings' => round($month_earnings, 2),//本月预估收益
            'level_name' => $levelName,
            'distribution_agent_name' => $distribution['distribution_agent_name'],
        ];
        return $data;
    }

    /**
     * Notes: 日期转化为查询方法,不存在默认查当天
     * Author: Darren
     * DateTime: 2023-07-13 17:23
     */
    protected static function dateToFunction($date = 'today')
    {
        $date_arr['today'] = ['whereDay', 'today'];
        $date_arr['yesterday'] = ['whereDay', 'yesterday'];
        $date_arr['this_month'] = ['whereMonth', 'this month'];
        $date_arr['last_month'] = ['whereMonth', 'first day of last month'];
        $date_arr['all'] = ['', ''];
        return $date_arr[$date] ?? ['whereDay', 'today'];
    }

    /**
     * Notes:
     * Author: Darren
     * DateTime: 2023-07-13 17:23
     */
    protected static function dateToWhereTime($filed, $date = 'today')
    {
        $today_start = strtotime(date('Y-m-d', time()));
        $date_arr['today'] = [$filed, '>', $today_start];
        $date_arr['yesterday'] = ['whereDay', 'yesterday'];
        $date_arr['this_month'] = ['whereMonth', 'this month'];
        $date_arr['last_month'] = ['whereMonth', 'last month'];
        return $date_arr[$date] ?? [];
    }

    /**
     * Notes:团队数据销售列表,包括自己的团队及自己孵化的团队
     * Author: Darren
     * DateTime: 2023-07-13 15:42
     * @param $userId
     * @param string $date 查询日期
     * @param int $is_sub 是否是下级团队
     */
    public static function teamlist($userId, $get, $user_info)
    {
        list($method, $date) = DistributionLogic::dateToFunction($get['date'] ?? 'today');
        $is_sub = $get['is_sub'] ?? 0;
        $field = 'user_id,IFNULL(sum(money), 0) as total_earnings, IFNULL(sum(total_price),0) as total_price,count(DISTINCT order_id) as count_order_num, count(DISTINCT buyer_user_id) as count_user_num,distribution_user_id,distribution_level_id';
        $distribution_field = 'id,user_id,distribution_agent_name,mobile,distribution_time,level_id,is_distribution,is_freeze';
        //自己的团队
        if ($is_sub == 0) {
            //查询分销单
            $where_order[] = ['level', '=', 2];//团队成员自己店铺销售的
//            $where_order[] = ['level_id', '=', DistributionEnum::DISTRIBUTION_LEVEL_ID_A];//下单时候 自己是分销商
//            $where_order[] = ['distribution_level_id', '=', DistributionEnum::DISTRIBUTION_LEVEL_ID_B];//下单时候店铺等级是店主
            $where_order[] = ['user_id', '=', $userId];//分佣用户是自己
//            $where_order[]=['distribution_user_id', 'in', $team_user_id_arr];
            $where_order[] = ['status', '<>', DistributionOrderGoodsEnum::STATUS_ERROR];
            $data = DistributionOrderGoods::where($where_order);
            if ($method) {
                $data = $data->$method('create_time', $date);
            }
//            $orderfield = 'total_price';
//            $ordertype = 'asc';
//
//            if(isset($get['order'])) {
//                $orderfield = 'count_order_num';
//                $ordertype = $get['order'];
//            }
//            if(isset($get['fans'])){
//                $orderfield = 'count_user_num';
//                $ordertype = $get['fans'];
//            }
//            if(isset($get['money'])){
//                $orderfield = 'total_price';
//                $ordertype = $get['money'];
//            }
            $data = $data->field($field)
                ->group('distribution_user_id')
                ->order('total_price asc')
//                ->order($orderfield, $ordertype)
                ->select()
                ->toArray();
            //查询当前自己下级店铺
            $where_team = [
                'first_leader' => $userId,
                'is_distribution' => 1,
//                'level_id'=>DistributionEnum::DISTRIBUTION_LEVEL_ID_B   //自己下级店铺数据, 不包含自己店铺
            ];
            $team_user_arr = Distribution::with(['user'])->where($where_team)->order('id desc')->field($distribution_field)->select()->toArray();
            $team_user_arr = array_column($team_user_arr, null, 'user_id');
            $team_user_id_arr = array_keys($team_user_arr);
            $extend_user_id = [];
            foreach ($data as $val) {//排查当前不属于自己下级店铺, 但是之前属于自己且有订单的用户
                if ($val['distribution_level_id'] == DistributionEnum::DISTRIBUTION_LEVEL_ID_B && !in_array($val['distribution_user_id'], $team_user_id_arr)) {
                    $extend_user_id[] = $val['distribution_user_id'];
                }
            }
            $team_user_arr2 = $team_user_id_arr2 = [];
            if ($extend_user_id) {
                $where_team = [
                    'user_id' => $extend_user_id,
                    //'is_distribution'=>1, 查询历史数据不需要这些条件
                    //'level_id'=>DistributionEnum::DISTRIBUTION_LEVEL_ID_B   //自己下级店铺
                ];
                $team_user_arr2 = Distribution::with(['user'])->where($where_team)->order('id desc')->field($distribution_field)->select()->toArray();
                $team_user_arr2 = array_column($team_user_arr2, null, 'user_id');
            }
            $team_user_arr = array_merge($team_user_arr, $team_user_arr2);
        } else {
            // 1%的收益
            $where_order[] = ['user_id', '=', $userId];
            $where_order[] = ['level', '=', DistributionEnum::DISTRIBUTION_LEVEL_THIRD];//团队成员自己店铺销售的
//            $where_order[] = ['level_id', '=', DistributionEnum::DISTRIBUTION_LEVEL_ID_A];//下单时候 自己是分销商
//            $where_order[] = ['distribution_level_id', 'in', [DistributionEnum::DISTRIBUTION_LEVEL_ID_B, DistributionEnum::DISTRIBUTION_LEVEL_ID_A]];//下单时候店铺等级是店主/代理商
            $where_order[] = ['status', '<>', DistributionOrderGoodsEnum::STATUS_ERROR];
            $data = DistributionOrderGoods::where($where_order);
            if ($method) {
                $data = $data->$method('create_time', $date);
            }
            $data = $data->field($field)
                ->group('user_id')
                ->order('total_price asc')
                ->select()
                ->toArray();
            //自己下级代理商团队
            $where_team = [
                'first_leader' => $userId,
                'is_distribution' => 1,
//                'level_id'=>DistributionEnum::DISTRIBUTION_LEVEL_ID_A   //自己下级代理商团队
            ];
            $team_user_arr = Distribution::with(['user'])->where($where_team)->order('id desc')->field($distribution_field)->select()->toArray();
            $team_user_arr = array_column($team_user_arr, null, 'user_id');
            $team_user_id_arr = array_keys($team_user_arr);
            $extend_user_id = [];
            foreach ($data as $val) {//排查当前不属于自己下级代理商团队, 但是之前属于自己且有订单的用户
                if ($val['distribution_level_id'] == DistributionEnum::DISTRIBUTION_LEVEL_ID_A && !in_array($val['distribution_user_id'], $team_user_id_arr)) {
                    $extend_user_id[] = $val['distribution_user_id'];
                }
            }
            $team_user_arr2 = [];
            if ($extend_user_id) {
                $where_team = [
                    'user_id' => $extend_user_id,
//                    'is_distribution'=>1,
//                    'level_id'=>DistributionEnum::DISTRIBUTION_LEVEL_ID_A   //自己下级代理商团队
                ];
                $team_user_arr2 = Distribution::with(['user'])->where($where_team)->order('id desc')->field($distribution_field)->select()->toArray();
                $team_user_arr2 = array_column($team_user_arr2, null, 'user_id');
            }
            $team_user_arr = array_merge($team_user_arr, $team_user_arr2);
        }
        //统计合计
        $total['total_earnings'] = $total['total_price'] = $total['count_order_num'] = $total['count_user_num'] = 0;
        $result = [];
        if ($data) {
            foreach ($data as $k => $val) {
                $total['total_earnings'] += $val['total_earnings'];
                $total['total_price'] += $val['total_price'];
                $total['count_order_num'] += $val['count_order_num'];
                $total['count_user_num'] += $val['count_user_num'];
                $result[$val['distribution_user_id']] = $val;
            }
            $total['total_earnings'] = money($total['total_earnings']);
            $total['total_price'] = money($total['total_price']);
        }
        //无数据的用户
        $user_default_data = [
            'total_earnings' => '0.00',
            'total_price' => '0.00',
            'count_order_num' => 0,
            'count_user_num' => 0
        ];
        //查找申请成为分销商的用户
        $apply_list = DistributionMemberApply::where(['invite_code' => $user_info['distribution_code']])
            ->with(['user' => function ($query) {
                $query->field(['id', 'first_leader', 'nickname', 'avatar', 'sn', 'mobile']);
            }])
            ->where('status', '=', 0)->select()->toArray();
        $apply_list = array_column($apply_list, null, 'user_id');
        if ($apply_list) {
            $team_user_arr = array_merge($apply_list, $team_user_arr);
        }
        foreach ($team_user_arr as &$user) {
            if (isset($user['status']) && $user['status'] == 0) {
                //还不是店主,还没通过的
                $user['is_distribution'] = 0;
                $user['business_card'] = $user['business_card'] ? UrlServer::getFileUrl($user['business_card']) : '';
                $user['business_license_front'] = isset($user['business_license_front']) && $user['business_license_front'] ? UrlServer::getFileUrl($user['business_license_front']) : '';
            } else {
                $user['status'] = 1;
            }
            //avatar, mobile, sn, nickname
            $uid = $user['user_id'];
            $user['user']['avatar'] = isset($user['user']['avatar']) && $user['user']['avatar'] ? UrlServer::getFileUrl($user['user']['avatar']) : '';
            //有数据使用自己的统计数据,没有的使用 默认值
            $user = isset($result[$uid]) ? array_merge($user, $result[$uid]) : array_merge($user, $user_default_data);
        }
        if(isset($get['order'])) {
            if($get['order']=='desc'){//降序
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($b['count_order_num']) - floatval($a['count_order_num']);
                });
            }else{
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($a['count_order_num']) - floatval($b['count_order_num']);
                });
            }

        }
        if(isset($get['fans'])){
            if($get['fans']=='desc'){//降序
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($b['count_user_num']) - floatval($a['count_user_num']);
                });
            }else{
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($a['count_user_num']) - floatval($b['count_user_num']);
                });
            }
        }
        if(isset($get['money'])){
            if($get['money']=='desc'){//降序
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($b['total_price']) - floatval($a['total_price']);
                });
            }else{
                usort($team_user_arr, function($a, $b) {
                    // 优先比较status
                    if($a['status'] == 0 && $b['status'] != 0) return -1;
                    if($a['status'] != 0 && $b['status'] == 0) return 1;
                    // 其次比较count_order_num
                    return floatval($a['total_price']) - floatval($b['total_price']);
                });
            }
        }
        $total['is_sub'] = $is_sub;
        return ['total' => $total, 'lists' => array_values($team_user_arr)];
    }

    /**
     * 自己团队(不包含自己店铺)统计合计数据查询
     * Notes: 代理商自己下级团队销售数据
     * 日期分为: today,this_month,last_month,
     * Author: Darren
     * DateTime: 2023-06-27 10:17
     */
    public static function getTeamsaleInfo($userId, $date = 'today')
    {
//        $where_team=[
//            'first_leader'=>$userId,
//            'is_distribution'=>1,
//            'level_id'=>DistributionEnum::DISTRIBUTION_LEVEL_ID_B   //自己下级店铺数据, 不包含自己店铺
//        ];
//        $team_user_id_arr = Distribution::where($where_team)->select()->column('user_id');
        $where_order = [
            ['user_id', '=', $userId],
            ['level', '=', 2],//团队成员自己店铺销售的
//            ['level_id', '=', DistributionEnum::DISTRIBUTION_LEVEL_ID_A],
//            ['distribution_level_id', '=', DistributionEnum::DISTRIBUTION_LEVEL_ID_B],//下单时候店铺等级是店主
            ['status', '<>', DistributionOrderGoodsEnum::STATUS_ERROR],
        ];
        $field = 'IFNULL(sum(money), 0) as today_earnings, IFNULL(sum(total_price),0) as today_total_price, count(DISTINCT order_id) as today_order_num';
        list($method, $date) = DistributionLogic::dateToFunction($date);
        $data = DistributionOrderGoods::where($where_order);
        if ($method) {
            $data = $data->$method('create_time', $date);
        }
        $data = $data->where($where_order)
            ->field($field)
            ->find()
            ->toArray();
        return $data;
    }

    /***
     * 获取自身及上级信息
     */
    public static function myLeader($userId)
    {
        $field = 'nickname,avatar,is_distribution,mobile,first_leader,distribution_code,earnings,earnest_money,partner_earnings';

        $user = User::field($field)->where(['id' => $userId, 'del' => 0])->findOrEmpty();

        $first_leader = User::field('nickname,mobile')
            ->where(['id' => $user['first_leader'], 'del' => 0])
            ->findOrEmpty();

        $user['avatar'] = UrlServer::getFileUrl($user['avatar']);
        return [
            'user' => $user,
            'leader' => $first_leader,
        ];
    }

    /**
     * 填写邀请码
     */
    public static function code($post)
    {
        try {
            Db::startTrans();

            $firstLeader = User::field(['id', 'first_leader', 'second_leader', 'third_leader', 'ancestor_relation', 'user_integral'])
                ->where(['distribution_code' => $post['code']])
                ->findOrEmpty();
            if ($firstLeader->isEmpty()) {
                throw new \think\Exception('无效的邀请码');
            }

            // 上级
            $first_leader_id = $firstLeader['id'];
            // 上上级
            $second_leader_id = $firstLeader['first_leader'];
            // 上上上级
            $third_leader_id = $firstLeader['second_leader'];
            // 拼接关系链
            $firstLeader['ancestor_relation'] = boolval($firstLeader['ancestor_relation']) ?? ''; // 清空null值及0
            $my_ancestor_relation = $first_leader_id . ',' . $firstLeader['ancestor_relation'];
            // 去除两端逗号
            $my_ancestor_relation = trim($my_ancestor_relation, ',');

            $user = User::findOrEmpty($post['user_id']);
            // 旧关系链
            if (!empty($user->ancestor_relation)) {
                $old_ancestor_relation = $user->id . ',' . $user->ancestor_relation;
            } else {
                $old_ancestor_relation = $user->id;
            }

            $data = [
                'first_leader' => $first_leader_id,
                'second_leader' => $second_leader_id,
                'third_leader' => $third_leader_id,
                'ancestor_relation' => $my_ancestor_relation,
                'update_time' => time()
            ];

            // 更新当前用户的分销关系
            User::where(['id' => $post['user_id']])->update($data);

            //更新当前用户下级的分销关系
            $data = [
                'second_leader' => $first_leader_id,
                'third_leader' => $second_leader_id,
                'update_time' => time()
            ];
            User::where(['first_leader' => $post['user_id']])->update($data);

            //更新当前用户下下级的分销关系
            $data = [
                'third_leader' => $first_leader_id,
                'update_time' => time()
            ];
            User::where(['second_leader' => $post['user_id']])->update($data);

            //更新当前用户所有后代的关系链
            $posterityArr = User::field('id,ancestor_relation')
                ->whereFindInSet('ancestor_relation', $post['user_id'])
                ->select()
                ->toArray();
            $updateData = [];
            $replace_ancestor_relation = $post['user_id'] . ',' . $my_ancestor_relation;
            foreach ($posterityArr as $item) {
                $updateData[] = [
                    'id' => $item['id'],
                    'ancestor_relation' => trim(str_replace($old_ancestor_relation, $replace_ancestor_relation, $item['ancestor_relation']), ',')
                ];
            }
            // 批量更新
            (new User())->saveAll($updateData);

            //邀请会员赠送积分
            $invited_award_integral = ConfigServer::get('marketing', 'invited_award_integral', 0);
            if ($invited_award_integral > 0) {
                // 增加上级积分
                $firstLeader->user_integral += (int)$invited_award_integral;
                $firstLeader->save();
                // 增加上级积分变动记录
                AccountLogLogic::AccountRecord($firstLeader['id'], $invited_award_integral, 1, AccountLog::invite_add_integral);
            }

            //通知用户
            event('Notice', [
                'scene' => NoticeEnum::INVITE_SUCCESS_NOTICE,
                'params' => [
                    'user_id' => $first_leader_id,
                    'lower_id' => $post['user_id'],
                    'join_time' => date('Y-m-d H:i:s', time())
                ]
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function formatOrderStatusQuery($queryOrderStatus)
    {
        $whereExt = [];
        switch ($queryOrderStatus) {
            case 1:
                $whereExt[] = ['o.order_status', '=', OrderEnum::ORDER_STATUS_DELIVERY];
                break;
            case 2:
                $whereExt[] = ['o.order_status', '=', OrderEnum::ORDER_STATUS_GOODS];
                break;
            case 3:
                $whereExt[] = ['o.order_status', '=', OrderEnum::ORDER_STATUS_DOWN];
                break;
            case 4:
                $whereExt[] = ['o.order_status', '=', OrderEnum::ORDER_STATUS_COMPLETE];
                break;
            default:   //全部
                $whereExt = [];
                break;
        }
        return $whereExt;
    }

    /**
     * 分销订单
     */
    public static function order($get)
    {
        $where[] = ['d.user_id', '=', $get['user_id']];

        $whereOr =[];
        // 关键词
        if(isset($get['keyword']) && !empty($get['keyword'])) {
            $keywords = trim($get['keyword']);
            $keywords = replaceNullTo($keywords);
            if ($keywords){
                if (is_numeric($keywords)){
                    $whereOr[] = ['o.order_sn', 'like','%'.$keywords.'%'];
                    $whereOr[] = ['o.mobile', 'like','%'.$keywords.'%'];
                }elseif(is_string($keywords)){
                    $whereOr[] = ['o.consignee', 'like','%'.$keywords.'%'];
                    $whereOr[] = ['og.goods_name', 'like','%'.$keywords.'%'];
                }
            }
//            if (!strpos($keywords, ' ') === false){
//                $keywords_arr = explode(' ', $keywords);
//                if ($keywords_arr){
//                    foreach ($keywords_arr as $item){
//                        $whereOr[] = ['name','like','%'.$item.'%'];
//                    }
//                }
//            }else{
//                $where[] = ['name','like','%'.$keywords.'%'];
//            }
        }
        if(isset($get['order_no']) && $get['order_no']){
            $where[] = ['o.order_sn','like','%'.$get['order_no'].'%'];
        }
        if (isset($get['order_status']) && $get['order_status']) {
            $where = array_merge($where, self::formatOrderStatusQuery($get['order_status']));
        }
        if (isset($get['buyer_user_id']) && $get['buyer_user_id']) {
            $where[] = ['d.buyer_user_id', '=', $get['buyer_user_id']];
        }
        if (isset($get['consignee']) && $get['consignee']) {
            $where[] = ['o.consignee','like','%'.$get['consignee'].'%'];
        }

        if (isset($get['status']) && in_array($get['status'], [1, 2, 3])) {
            $where[] = ['d.status', '=', $get['status']];
        }
        if (isset($get['level']) && in_array($get['level'], [1, 2])) {
            $where[] = ['d.level', '=', $get['level']];
        }
        if (isset($get['level_id']) && in_array($get['level_id'], [1, 2, 3])) {
            $where[] = ['d.level_id', '=', $get['level_id']];
        }
//          买家信息搜索
        $where_user = [];
        if(isset($get['nickname']) && $get['nickname']){
            $where_user[] = ['nickname', 'like','%'.$get['nickname'].'%'];
        }
        if(isset($get['mobile']) && $get['mobile']){
            $where_user[] = ['mobile', 'like','%'.$get['mobile'].'%'];
        }
        if ($where_user){
            $user_id_list = User::where($where_user)->select()->toArray();
            $user_id_list = array_column($user_id_list, 'id');
            if ($user_id_list) {
                $where[] = ['o.user_id', 'in', $user_id_list];
            }else{
                $where[] = ['o.user_id', '=', 0];
            }
        }
        $field = 'd.id, d.order_id, d.create_time, d.total_price,d.order_goods_id, d.money,d.buyer_user_id, d.goods_num, d.status, d.status as statusDesc, 
            o.order_sn,o.order_status,o.pay_status, o.shipping_status, o.is_comment,o.confirm_take_time,o.mobile,o.consignee,
            og.goods_id,og.image as goods_image, og.goods_name, og.after_sale_id,og.refund_status, og.total_pay_price as pay_price, og.spec_value as spec_value_str';
        list($method, $date) = DistributionLogic::dateToFunction($get['date'] ?? 'all');
        $total_sum = DistributionOrderGoods::alias('d')
            ->field('sum(d.money) as total_earnings, sum(d.total_price) as total_price, count(DISTINCT d.order_id) as total_order_num, count(1) as total_count')
            ->leftJoin('order_goods og', 'og.id = d.order_goods_id')
            ->leftJoin('order o', 'o.id = og.order_id')
            ->where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            });
        if ($method) {
            $total_sum = $total_sum->$method('d.create_time', $date);
        }
        $total_sum = $total_sum->find()->toArray();
        $count = $total_sum['total_count'] ?? 0;
        $lists = DistributionOrderGoods::alias('d')
            ->field($field)
            ->leftJoin('order_goods og', 'og.id = d.order_goods_id')
            ->leftJoin('order o', 'o.id = og.order_id');
//            ->leftJoin('goods g', 'og.goods_id=g.id');
//            ->leftJoin('goods_item gi', 'og.item_id=gi.id');
        if ($method) {
            $lists = $lists->$method('d.create_time', $date);
        }
        $lists = $lists->where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->order('d.id desc')
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();
        if ($lists) {
            $buyer_user_id_arr = array_values(array_unique(array_column($lists, 'buyer_user_id')));
            $buyer_user_list = User::where([['id', 'in', $buyer_user_id_arr]])->column('id,avatar,nickname,mobile', 'id');
            foreach ($buyer_user_list as &$value) {
                $value['avatar'] = UrlServer::getFileUrl($value['avatar']);
            }
        }
        foreach ($lists as &$item) {
            $item['buyer'] = $buyer_user_list[$item['buyer_user_id']] ?? [];
            $item['goods_image'] = empty($item['goods_image']) ? '' : UrlServer::getFileUrl($item['goods_image']);
        }
        //根据 订单状态, 售后状态, 分佣状态 确定最后的状态
        $lists = self::modifyStatusWithAfterSale($lists);

        $data = [
            'list' => $lists,
            'page' => $get['page_no'],
            'size' => $get['page_size'],
            'count' => $count,
            'total_sum' => $total_sum,
            'more' => is_more($count, $get['page_no'], $get['page_size'])
        ];
        return $data;
    }

    /**
     * Notes: 重置修改带有售后单的分销单状态
     * Author: Darren
     */
    public static function modifyStatusWithAfterSale($distributionOrderLists)
    {
        if (!$distributionOrderLists) {
            return [];
        }
        $distribution_id_after_sale_id_arr = array_column($distributionOrderLists, 'id', 'after_sale_id');
        unset($distribution_id_after_sale_id_arr[0], $distribution_id_after_sale_id_arr['']);
        if ($distribution_id_after_sale_id_arr) {//有售后
            $after_sale_id_arr = array_keys($distribution_id_after_sale_id_arr);
//            $dis_after_sale_id_arr = array_flip($dis_after_sale_id_arr);
            //售后状态
            $after_sale_status_list = AfterSale::where('id', 'in', $after_sale_id_arr)
                ->where('del', '=', 0)
                ->column('status', 'id');
        }
        foreach ($distributionOrderLists as $k => $value) {
            $value['order_status_text'] = OrderModel::getOrderStatus($value['order_status']);
            $value['aftersale_status'] = isset($value['after_sale_id']) && isset($after_sale_status_list[$value['after_sale_id']]) ? $after_sale_status_list[$value['after_sale_id']] : -1;
            $value['aftersale_status_text'] = $value['aftersale_status'] >= 0 ? \app\common\model\after_sale\AfterSale::getStatusDesc($value['aftersale_status']) : '';
            $value['statusDesc'] = self::calcLogicStatus($value);
            $distributionOrderLists[$k] = $value;
        }
        return $distributionOrderLists;
    }

    /**
     * Notes: 根据订单、售后单状态计算显示分销订单的逻辑状态
     * Author: Darren
     * DateTime: 2024-11-12 10:55
     */
    public static function calcLogicStatus($distributionOrderGoods)
    {
        //组合判断返回的状态
        //分佣状态: status statusDesc 1-待返佣；2-已结算；3-已失效；4; 提现中,5已提现
        //订单状态: order_status 0-待付款;1-待发货;2-待收货;3-已完成; 4-已关闭
        //售后状态: aftersale_status aftersale_status_text 0-申请退款;1-商家拒绝;2-商品待退货;3-商家待收货;4-商家拒收货;5-等待退款;6-退款成功
        //发货前:待发货
        //发货后:待收货
        //有申请售后:展示售后状态
        $statusDesc = $distributionOrderGoods['statusDesc'];
        //待发货
        if ($distributionOrderGoods['order_status'] == 1) {
            $statusDesc = $distributionOrderGoods['order_status_text'];
            return $statusDesc;
        }
        //发货后
        if ($distributionOrderGoods['order_status'] == 2) {
            //有售后状态, 返回售后状态
            if ($distributionOrderGoods['aftersale_status_text']) {
                $statusDesc = $distributionOrderGoods['aftersale_status_text'];
            } else {
                if ($distributionOrderGoods['confirm_take_time'] > 0) {
                    $statusDesc = '已收货，待评价';
                } else {
                    $statusDesc = $distributionOrderGoods['order_status_text'];
                }
            }
        }
        //订单确认退款之后才将 更新分销单状态为 已失效
//        if ($distributionOrderGoods['order_status'] == 4){
////            $statusDesc = $distributionOrderGoods['aftersale_status_text'];
//        }
        return $statusDesc;
    }

    /**
     * 月度账单
     */
    public static function monthBill($get)
    {
        $field = [
            "FROM_UNIXTIME(d.create_time,'%Y年%m月') as date",
            "FROM_UNIXTIME(d.create_time,'%Y') as year",
            "FROM_UNIXTIME(d.create_time,'%m') as month",
            'sum(d.money) as total_money',
            'count(d.id) as order_num'
        ];
        $count = DistributionOrderGoods::alias('d')
            ->field($field)
            ->leftJoin('order_goods g', 'g.id = d.order_goods_id')
            ->leftJoin('order o', 'o.id = g.order_id')
            ->where(['d.user_id' => $get['user_id']])
            ->where('d.status', 'in', [1, 2])
            ->group('date')
            ->count();

        $lists = DistributionOrderGoods::alias('d')
            ->field($field)
            ->leftJoin('order_goods g', 'g.id = d.order_goods_id')
            ->leftJoin('order o', 'o.id = g.order_id')
            ->where(['d.user_id' => $get['user_id']])
            ->where('d.status', 'in', [1, 2])
            ->order('d.id desc')
            ->page($get['page_no'], $get['page_size'])
            ->group('date')
            ->select()
            ->toArray();

        $data = [
            'list' => $lists,
            'page' => $get['page_no'],
            'size' => $get['page_size'],
            'count' => $count,
            'more' => is_more($count, $get['page_no'], $get['page_size'])
        ];
        return $data;
    }

    /**
     * 月度明细
     */
    public static function monthDetail($get)
    {
        $where[] = ['d.user_id', '=', $get['user_id']];

        $monthStr = $get['year'] . '-' . str_pad($get['month'], 2, '0', STR_PAD_LEFT);

        $field = 'd.create_time, d.money, d.goods_num, d.status, d.status as statusDesc, o.order_sn, o.id as order_id, og.goods_id, og.total_pay_price as pay_price, g.image as goods_image,gi.image as goods_item_image, g.name as goods_name, gi.spec_value_str';

        $count = DistributionOrderGoods::alias('d')
            ->where($where)
            ->whereMonth('d.create_time', $monthStr)
            ->count();

        $lists = DistributionOrderGoods::alias('d')
            ->field($field)
            ->leftJoin('order_goods og', 'og.id = d.order_goods_id')
            ->leftJoin('order o', 'o.id = og.order_id')
            ->leftJoin('goods g', 'og.goods_id=g.id')
            ->leftJoin('goods_item gi', 'og.item_id=gi.id')
            ->where($where)
            ->whereMonth('d.create_time', $monthStr)
            ->order('d.id desc, d.level desc')
            ->page($get['page_no'], $get['page_size'])
            ->select()
            ->toArray();
        if ($lists){
            foreach ($lists as &$item) {
                $item['goods_image'] = $item['goods_item_image'] ? UrlServer::getFileUrl($item['goods_item_image']) : UrlServer::getFileUrl($item['goods_image']);
            }
        }

        $data = [
            'list' => $lists,
            'page' => $get['page_no'],
            'size' => $get['page_size'],
            'count' => $count,
            'more' => is_more($count, $get['page_no'], $get['page_size'])
        ];
        return $data;
    }


    /**
     * Desc: 取消订单后更新分销订单为已失效
     * @param $order_id
     * @throws Exception
     * @throws \think\exception\PDOException
     */
    public static function setDistributionOrderFail($order_id)
    {
        //订单取消后更新分销订单为已失效状态
        return Db::name('distribution_order_goods d')
            ->join('order_goods og', 'og.id = d.order_goods_id')
            ->join('order o', 'o.id = og.order_id')
            ->where('o.id', $order_id)
            ->update([
                'd.status' => DistributionOrderGoodsEnum::STATUS_ERROR,
                'd.update_time' => time(),
            ]);
    }

    /**
     * @Notes: 分销佣金列表
     * @Author: 张无忌
     * @param $get
     * @param $user_id
     * @return bool|array
     */
    public static function commission($get, $user_id)
    {
        try {
            $where = [
                ['user_id', '=', $user_id],
            ];
            // 添加source_type筛选条件
            if (isset($get['source_type']) && $get['source_type'] !== '') {
                $where[] = ['source_type', '=', $get['source_type']];
            }else{
                $where[] = ['source_type', 'in', AccountLog::earnings_change];
            }
            
            // 添加pay_way筛选条件
            if (isset($get['pay_way']) && $get['pay_way'] !== '') {
                $where[] = ['pay_way', '=', $get['pay_way']];
            }
            
            // 添加pay_way_not筛选条件
            if (isset($get['pay_way_not']) && $get['pay_way_not'] !== '') {
                $where[] = ['pay_way', '<>', $get['pay_way_not']];
            }
            
            $whereOrderNum = $where; 
            $whereOrderNum[]=['source_type', '=', AccountLog::distribution_inc_earnings];
            
            $model = new AccountLog();
            $count = $model->where($where)->count();
            $order_count = $model->where($whereOrderNum)->group('source_sn')->count();
            $change_amount_total_add = $model->where($where)->where('change_type', '=', 1)->sum('change_amount');
            $change_amount_total_sub = $model->where($where)->where('change_type', '=', 2)->sum('change_amount');
            $change_amount_total = money($change_amount_total_add - $change_amount_total_sub);
            $lists = $model->field(['id,source_type,source_type as source_type_code,change_amount,change_type,create_time,source_id,source_sn,pay_way'])
                ->where($where)
                ->order('id', 'desc')
                ->page($get['page_no'] ?? 1, $get['page_size'] ?? 20)
                ->select();
            foreach ($lists as &$item) {
                $symbol = $item['change_type'] == 1 ? '+' : '-';
                $item['change_amount'] = $symbol . $item['change_amount'];
            }
            return [
                'list' => $lists,
                'page' => $get['page_no'] ?? 1,
                'size' => $get['page_size'] ?? 20,
                'count' => $count,
                'order_count' => $order_count,
                'change_amount_total' => $change_amount_total,
                'more' => is_more($count, $get['page_no'] ?? 1, $get['page_size'] ?? 20)
            ];

        } catch (\Exception $e) {
            static::$error = $e->getMessage();
            return false;
        }
    }

    public static function fixAncestorRelation()
    {
        try {
            $userList = User::select()->toArray();
            if (empty($userList)) {
                throw new \Exception('没有用户，无需修复');
            }

            $updateEmptyData = [];
            $updateData = [];
            foreach ($userList as $user) {
                $my_ancestor_relation = self::myAncestorRelation($user);
                $updateEmptyData[] = ['id' => $user['id'], 'ancestor_relation' => ''];
                $updateData[] = ['id' => $user['id'], 'ancestor_relation' => $my_ancestor_relation];
            }
            // 先清除所有关系链
            (new User())->saveAll($updateEmptyData);
            // 重新设置关系链
            (new User())->saveAll($updateData);

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    public static function myAncestorRelation($user)
    {
        if (empty($user['first_leader'])) {
            return '';
        }

        return trim(self::findAncestorRelation($user['first_leader']), ',');
    }

    public static function findAncestorRelation($id, $flag = true)
    {
        static $ancestor_relation = '';
        if ($flag) {
            $ancestor_relation = '';
        }
        $ancestor_relation .= $id . ',';
        $user = User::findOrEmpty($id);
        if (empty($user['first_leader'])) {
            return $ancestor_relation;
        }
        return self::findAncestorRelation($user['first_leader'], false);
    }


    /**
     * @notes 获取背景海报
     * @return array|mixed|string|null
     * <AUTHOR>
     * @date 2021/11/29 11:35
     */
    public static function getPoster()
    {
        $poster = ConfigServer::get('invite', 'poster', '/images/share/share_user_bg.png');
        $poster = empty($poster) ? $poster : UrlServer::getFileUrl($poster);
        $slogan = ConfigServer::get('invite', 'slogan');
        return ['poster' => $poster, 'slogan' => $slogan];

    }

    /**
     * Notes: 设置分销店铺信息
     * Author: Darren
     * DateTime: 2023-05-26 11:55
     */
    public static function setInfo($data)
    {
        $result['error'] = '';
        $distribution = Distribution::where('user_id', $data['user_id'])->find();
        if (!$distribution) {
            $result['error'] = '未获取店铺';
            return $result;
        }
        if (!$distribution['is_distribution']) {
            $result['error'] = '该账户不是分销商';
            return $result;
        }
        if ($distribution['is_freeze']) {
//            $result['error'] ='店铺已经被冻结';
//            return $result;
        }
        if ($data['distribution_id'] != $distribution['id']) {
            $result['error'] = '无权限设置该店铺信息';
            return $result;
        }
        $update = [];
        if ($data['distribution_agent_name']) {
            $distribution_len = strlen($data['distribution_agent_name']);
            if ($distribution_len > 40 || $distribution_len < 3) {
                $result['error'] = '店铺长度不合规';
                return $result;
            }
            $update['distribution_agent_name'] = $data['distribution_agent_name'];
        }
        if ($data['business_card']) {
            $update['business_card'] = $data['business_card'];
        }
        if (isset($data['is_mnp_notice'])) {
            $update['is_mnp_notice'] = $data['is_mnp_notice'];
        }
        if (isset($data['remark']) && $data['remark']) {
            $update['remark'] = $data['remark'];
        }
        if ($update) {
            $update['update_time'] = time();
            Distribution::update($update, ['id' => $distribution['id']]);
        }
        return $result;
    }

    /**
     * 实现功能: 根据用户登录状态/用户上次访问的分销店铺/用户自己的分销店铺状态/请求头中的distributionid/用户提交的分销店铺邀请码($invite_code), 是否是登录(is_login), 确定本次实际要返回的店铺;
     * 1. 未登录; 无邀请码, 无distributionid, 提示错误未找到店铺;
     * 2. 未登录; 有邀请码, 无distributionid, 返回邀请码对应的店铺
     * 3. 未登录; 无邀请码, 有distributionid, 返回distributionid对应的店铺
     * 4. 已登录; 如果是店主(is_distribution == 1), 直接返回自己的店铺;不管自己店铺是否锁定(is_freeze)
     * 5. 已登录; 如果不是店主, 
     *          根据判断切换店铺的设置(user.change_distribution_set), 
     *              如果是2 锁定模式,禁止切换,始终使用上次访问(last_distribution_id)的店铺, 
     *                      如果上次访问的店铺被锁定,并且有邀请码,则返回邀请码对应的店铺; 如果邀请码对应的店铺不存在或者被锁定并且是登录($is_login)接口, 则使用 distribution_id 对应的店铺
     *              如果是1 提示切换模式   
     *                      则查找邀请码对应的分销店铺.并且更新last_distribution_id
     *              
     * 6. 已登录; 如果不是店主, 有distributionid, 且distributionid是自己的, 直接返回自己的店铺;

     * 
     * Notes: 验证分销商信息 是否有效
     * Author: Darren
     * DateTime: 2023/4/18 8:56
     */
    public static function getdistribution($user_id = 0, $distribution_id = 0, $invite_code = '', $is_die = 1, $is_login=0)
    {
        $change_distribution_set = config('user.change_distribution_set', 1);   //  切换分销店铺的设置, 1 提示切换; 2 锁定模式,禁止切换,始终使用第一次的店铺; 3 自动默认切换
        if ($change_distribution_set == 2){//锁定模式
            if(!$is_login){
                $distribution_id = 0;
            }
        }
        $store_status = ConfigServer::get('website', 'store_status', 0);//开放分销店铺模式
        $default_distribution_id = ConfigServer::get('invite', 'default_distribution_id', 1);//默认分销商店铺
        $distribution = [];
        $distribution_default = [
            'id' => 0,
            'distribution_agent_name' => '',
            'business_card' => '',
            'user_id' => 0,
            'avatar' => '',
            'remark' => '',
            'level_id' => 0,
            'is_mnp_notice' => 0
        ];
        //未开启分销商模式
        if ($store_status != 10) {
            return $distribution_default;
        }
        $user_info = User::where('id', '=', $user_id)->field('id,last_distribution_id,login_scene_code,distribution_code')->find();
        $last_distribution_id = $user_info['last_distribution_id'] ?? 0;
        $field = 'id,user_id,level_id,mobile,distribution_agent_name,business_card,first_leader,is_distribution,is_freeze,is_mnp_notice,remark';
        $distribution_id = is_numeric($distribution_id) ? $distribution_id : 0;
        //用户已经登录的情况
        if ($user_id) {
            $where_dis = [
                'user_id' => $user_id,
                'is_distribution' => 1
            ];
            //优先返回自己店铺,被冻结也返回自己店铺
            $distribution = Distribution::field($field)->where($where_dis)->find();
            //非店主
            if (!$distribution) {
                //没有分销店铺id, 没有邀请码, 返回上次登录的店铺
                if ($change_distribution_set == 2){//锁定模式
                    if ($last_distribution_id){
                        //上次访问的店铺
                        $where_dis = [
                            'id' => $last_distribution_id,
                            'is_distribution' => 1,
                            //'is_freeze'=>0
                        ];
                        $distribution = Distribution::field($field)->where($where_dis)->find();
                        if ($distribution && isset($distribution['is_freeze']) && $distribution['is_freeze']){
                            $distribution = [];
                            //店铺被冻结 有邀请码则返回新店铺
                            if ($invite_code){
                                $invite_user_id = User::where('distribution_code', $invite_code)->value('id');
                                $where_dis = [
                                    'user_id' => $invite_user_id,
                                    'is_distribution' => 1,
                                ];
                                $distribution = Distribution::field($field)
                                    ->where($where_dis)
                                    ->find();
                                if (!$distribution && $distribution_id){
                                    $distribution = Distribution::field($field)
                                        ->where('id', '=', $distribution_id)
                                        ->where('is_distribution', '=', 1)
                                        ->find();
                                }
                            }
                            if (!$distribution && $distribution_id){
                                $distribution = Distribution::field($field)
                                    ->where('id', '=', $distribution_id)
                                    ->where('is_distribution', '=', 1)
                                    ->find();
                            }

                        }
                    }
                    if (!$distribution && $invite_code){
                        //没有上次访问的店铺 //???特殊情况?? 使用邀请码
                        $invite_user_id = User::where('distribution_code', $invite_code)->value('id');
                        $where_dis = [
                            'user_id' => $invite_user_id,
                            'is_distribution' => 1,
                        ];
                        $distribution = Distribution::field($field)
                            ->where($where_dis)
                            ->find();
                        $user_info['last_distribution_id'] = $distribution['id'];
                        $user_info->save();
                    }

                }elseif($change_distribution_set == 1){ //如果是切换模式
                    if ($invite_code){
                        $invite_user_id = User::where('distribution_code', $invite_code)->value('id');
                        $where_dis = [
                            'user_id' => $invite_user_id,
                            'is_distribution' => 1,
                        ];
                        $distribution = Distribution::field($field)
                            ->where($where_dis)
                            ->find();
                        //如果本次的邀请码对应的distribution_id 与数据库的last_distribution_id不同, 更新数据库last_distribution_id
                        if ($distribution && isset($user_info['last_distribution_id']) && $user_info['last_distribution_id'] != $distribution['id']) {
                            $user_info['last_distribution_id'] = $distribution['id'];
                            $user_info->save();
                        }
                    }

                }
            }
        }else{
            //未登录情况
            //有邀请码
            if ($invite_code) {
                $invite_user_id = User::where('distribution_code', $invite_code)->value('id');
                $where_dis = [
                    'user_id' => $invite_user_id,
                    'is_distribution' => 1,
//                    'is_freeze'=>0
                ];
                $distribution = Distribution::field($field)
                    ->where($where_dis)
                    ->find();
            }elseif($distribution_id){
                $where_dis = [
                    'id' => $distribution_id,
                    'is_distribution' => 1,
//                    'is_freeze'=>0
                ];
                $distribution = Distribution::field($field)
                    ->where($where_dis)
                    ->find();
            }
        }

        if ($distribution) {
            $distribution = $distribution->toArray();
            $distribution_user = User::field('avatar,distribution_code')->where('id', $distribution['user_id'])->find();
            $distribution['invite_code'] = $distribution_user['distribution_code'] ?? '';
            $distribution['avatar'] = isset($distribution_user['avatar']) && $distribution_user['avatar'] ? $distribution_user['avatar'] : ConfigServer::get('website', 'user_image', '');
            $distribution['avatar'] = UrlServer::getFileUrl($distribution['avatar']);
            $distribution['business_card'] = UrlServer::getFileUrl($distribution['business_card']);
            return $distribution;
        } else {
            if ($is_die) {
                return JsonServer::throw('未获取店铺信息', [], -100);
            } else {
                return $distribution_default;
            }
        }
    }

    public static function getDistributionGoods($get, $distribution_info)
    {
        $where=[
            'dg.is_distribution'=>1,
            'dg.level_id'=>0,
            'g.del'=>0,
            'g.status'=>1,
        ];
        if(isset($get['goods_name']) && $get['goods_name']){
            $where[] = ['g.name','like','%'.$get['goods_name'].'%'];
        }
        $goods_list = DistributionGoods::alias('dg')
            ->field('g.id,g.name,g.code,g.image,g.onsale_time,g.sales_actual,
            g.clicks,g.clicks_virtual,g.min_price,g.max_price,g.is_distribution,dg.sort')
            ->join('goods g','dg.goods_id=g.id')
            ->where($where)
            ->order('dg.sort desc')
            ->page($get['page_no'], $get['page_size'])
            ->select()->toArray();
        $count = DistributionGoods::alias('dg')
            ->field('g.id,g.name,g.code,g.image,g.onsale_time,g.sales_actual,
            g.clicks,g.clicks_virtual,g.min_price,g.max_price,g.is_distribution,dg.sort')
            ->join('goods g','dg.goods_id=g.id')
            ->where($where)
            ->count();

//        $level_id = Distribution::where('user_id', $get['user_id'])->value('level_id');
        if ($goods_list){
            $goods_id_arr = array_column($goods_list, 'id');
            $goods_list = GoodsLogic::getNormalGoodsCommission($goods_id_arr, $goods_list,$distribution_info);
        }
        $more = is_more($count, $get['page_no'], $get['page_size']);
        $data = [
            'lists'         => $goods_list,
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more,
//            'level_id'      => $level_id,
        ];
        return $data;
    }

    public static function getDistributionShareGoodsLink($goods_id_arr, $distributionid, $where){
        $distributionid = intval($distributionid);
        $where_query[] = ['type', '=', $where['type']];
        $where_query[] = ['url_type', '=', $where['url_type']];
        $goods_id_arr = array_values(array_unique($goods_id_arr));
        if (!$distributionid || !$goods_id_arr){
            return [];
        }
        $shop_user_id = Distribution::where('id', '=', $distributionid)->value('user_id');
        $invite_code = User::where('id', '=', $shop_user_id)->value('distribution_code');
        $result = DistributionShare::where('target_id', 'in', $goods_id_arr)
            ->field('id,target_id,type,invite_code,url,url_type')
            ->where($where_query)
            ->where('invite_code', '=', $invite_code)
            ->select()->toArray();
        if ($result){
            $result = array_column($result, null, 'target_id');
        }
        foreach ($goods_id_arr as $key => $goods_id) {
            if (!isset($result[$goods_id])){
                $param = [];
                //单独请求接口查询
                $param['id'] = $goods_id;
                $param['distributionId'] = $distributionid;
                $param['type'] = $where['type'];
                $link_info = [
                    'target_id'=>$goods_id,
                    'type'=>$where['type'],
                    'invite_code'=>$invite_code,
                ];
                if ($where['url_type'] == 2){
                    $link_info['url_type'] = 2;
                    $link_info['url'] = ShareLogic::getWechatMiniprogramUrl($shop_user_id, $param, 1);
                    $result[$goods_id] = $link_info;
                }elseif($where['url_type'] == 3){
                    $link_info['url_type'] = 3;
                    $link_info['url'] = ShareLogic::getWechatUrllink($shop_user_id, $param, 1);
                    $result[$goods_id] = $link_info;
                }
            }
        }
        return array_values($result);
    }
}