<?php
declare (strict_types = 1);

namespace app\common\http\middleware;

use app\middleware\Response;

class PreventDuplicateSubmit
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    // 中间件逻辑
    public function handle($request, \Closure $next)
    {
        $method = $request->method();
        if ($method == 'POST' || $method == 'PUT' || $method == 'DELETE') {
            $token = $request->header('token') ?? ''; //已经登录用户通过token验证重复提交
            $key = $token ?: $request->ip();//未登录用户通过ip验证
            $key = $key. $request->url() . json_encode($request->param());
            $file = $request->file();
            if ($file && isset($file['file'])) {//有上传文件
                $key = $key . md5($file['file']->getPathname());
            }
//            abort(429, '系统升级中请稍后再试');
            $key = md5($key);
            if (cache()->has($key)) {
                abort(429, '请求过于频繁');
            }
            cache()->set($key, true, 2); // 2秒内禁止重复提交
        }
        return $next($request);
    }
}
