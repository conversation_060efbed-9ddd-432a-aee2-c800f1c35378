<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\common\logic;

use app\common\enum\DistributionEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\PayEnum;
use app\common\model\AccountLog;
use app\common\model\after_sale\AfterSale;
use app\common\model\Delivery;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\order\OrderLog;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use expressage\Kd100;
use expressage\Kdniao;
use think\facade\Db;
use think\facade\Env;
use think\facade\Log;

/**
 * 订单公共处理逻辑类
 * 
 * @package app\common\logic
 */
class OrderCommonLogic
{
    /**
     * 获取物流轨迹信息
     * 
     * @param int $orderId 订单ID
     * @param array $orderDelivery 订单配送信息
     * @return array|false 物流轨迹数组或false
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/14 10:05 上午
     */
    public static function getShipping($orderId, $orderDelivery = [])
    {
        if (empty($orderDelivery)) {
            $orderDelivery = Delivery::where(['order_id' => $orderId])
                ->find();
        }
        
        if (!$orderDelivery || $orderDelivery['send_type'] == 2) {
            return [['暂无物流信息']];
        }
        
        $traces = [];
        
        // 已经签收了，则不再查询快递公司
        if ($orderDelivery['is_check'] == 1 && $orderDelivery['shipping_data']) {
            $shippingData = json_decode($orderDelivery['shipping_data'], true);
            if ($shippingData && isset($shippingData['data']) && $shippingData['data']) {
                foreach ($shippingData['data'] as $k => $val) {
                    $traces[$k] = [$val['time'], $val['context']];
                }
            }
        }
        
        if (empty($traces) && $orderDelivery['shipping_status']) {
            $traces = self::getShippingTrace(
                $orderDelivery['shipping_id'], 
                $orderDelivery['invoice_no'], 
                $orderDelivery['mobile'], 
                $orderDelivery['id']
            );
        }
        
        return $traces;
    }

    /**
     * 获取物流跟踪记录
     * 
     * @param int $shippingId 物流公司ID
     * @param string $invoiceNo 快递单号
     * @param string $mobile 手机号
     * @param int $deliveryId 配送记录ID
     * @param string $shippingCode 物流编码
     * @return array 物流轨迹数组
     * <AUTHOR>
     * @date 2024-07-04 9:07
     */
    public static function getShippingTrace(
        int $shippingId, 
        string $invoiceNo, 
        string $mobile, 
        int $deliveryId = 0, 
        string $shippingCode = ''
    ): array {
        $express = ConfigServer::get('express', 'way', '', '');
        $key = ConfigServer::get($express, 'appkey', '', '');
        $app = ConfigServer::get($express, 'appsecret', '', '');

        if (!$shippingId && $shippingCode) {
            $shippingId = Db::name('express')->where(['code100' => $shippingCode])->value('id');
        }

        if (empty($express) || empty($app) || empty($key)) {
            return [['暂无物流信息']];
        }

        // 获取物流配置
        // 快递配置设置为快递鸟时
        if ($express === 'kdniao') {
            $expressage = new Kdniao($app, $key, Env::get('app.app_debug', 'true'));
            $shippingField = 'codebird';
        } else {
            $expressage = new Kd100($app, $key, Env::get('app.app_debug', 'true'));
            $shippingField = 'code100';
        }
        
        // 快递编码
        if (!$shippingCode) {
            $shippingCode = Db::name('express')->where(['id' => $shippingId])->value($shippingField);
        }
        
        if (!$shippingCode) {
            return [];
        }
        
        // 获取物流轨迹
        if ($shippingCode === 'shunfeng' && $express === 'kdniao') {
            $expressage->logistics($shippingCode, $invoiceNo, substr($mobile, -4));
        } else {
            $expressage->logistics($shippingCode, $invoiceNo, $mobile);
        }
        
        $traces = $expressage->logisticsFormat();
        
        trace('OrderCommonLogic---shipping_data----------');
        trace($expressage->shipping_data);
        trace('$shippingCode');
        trace($shippingCode);
        trace('$invoiceNo');
        trace($invoiceNo);
        
        if ($deliveryId) {
            $updateDelivery = [
                'is_check' => $expressage->ischeck,
                'shipping_data' => $expressage->shipping_data,
                'last_check_time' => date('Y-m-d H:i:s', time())
            ];
            Delivery::update($updateDelivery, ['id' => $deliveryId]);
        }
        
        // 获取不到物流轨迹时
        if ($traces === false) {
            trace('shipping_data----------');
            trace($expressage->shipping_data);
            $error = $expressage->getError();
            $error = json_decode($error, true);
            
            if ($express === 'kdniao') {
                if ($error['Success'] == false) {
                    $traces = [[$error['Reason']]];
                }
            } else {
                if ($error['result'] == false) {
                    $traces = [[$error['message']]];
                }
            }
        } else {
            // 确保$traces是数组类型
            if (is_array($traces)) {
                foreach ($traces as &$item) {
                    $item = array_values(array_unique($item));
                }
            }
        }
        
        return $traces;
    }

    /**
     * 获取待结算的所有分销订单列表
     * 
     * @return array|false 返回有效的分销订单和无效的分销订单ID，如果没有数据返回false
     */
    public static function getWaitSettleDistributionOrderList()
    {
        // 1、获取结算时间
        $time = time();
        $afterSaleTime = ConfigServer::get('distribution', 'settlement_days', 7);
        $afterSaleTime = intval($afterSaleTime * 24 * 60 * 60);
        $ableSharingTime = $time - $afterSaleTime;
        // 2、直接查询可以结算的分销订单
        $model = new DistributionOrderGoods();
        $where = [
            ['dog.comment_time', 'between', [1, $ableSharingTime]],
            ['dog.status', '=', DistributionOrderGoodsEnum::STATUS_WAIT_HANDLE],
            ['dog.money', '>', 0],
            ['dog.status_update_error', '=', ''],
            ['dog.level', 'in', [DistributionEnum::DISTRIBUTION_LEVEL_FIRST, DistributionEnum::DISTRIBUTION_LEVEL_SECOND]],
            ['o.pay_way', 'in', [PayEnum::HFDG_WECHAT, PayEnum::HFDG_ALIPAY]],
            ['o.distribution_money_status', 'in', [Order::DISTRIBUTION_MONEY_STATUS_UNDONE, Order::DISTRIBUTION_MONEY_STATUS_PART]]
        ];

        // 使用联合查询直接获取符合条件的分销订单
        $allDistributionOrders = $model->alias('dog')
            ->leftjoin('order o', 'o.id = dog.order_id')
            ->leftjoin('distribution d', 'd.user_id = dog.user_id')
            ->where($where)
            ->field('dog.*,d.pay_partner_account_code, o.order_sn, o.transaction_id, o.pay_way, o.distribution_money_status, o.hf_seq_id')
            ->limit(1)
//            ->fetchSQL()
            ->select()
            ->toArray();
        if (empty($allDistributionOrders)) {
            return false;
        }
        // 一次性获取所有售后订单信息
        $allOrderGoodsIds = array_column($allDistributionOrders, 'order_goods_id');
        $allAfterSales = AfterSale::where('order_goods_id', 'in', $allOrderGoodsIds)
            ->where('del', 0)
            ->select()
            ->toArray();

        // 按订单商品ID分组
        $afterSalesByOrderGoodsId = [];
        foreach ($allAfterSales as $afterSale) {
            $afterSalesByOrderGoodsId[$afterSale['order_goods_id']] = $afterSale;
        }

        // 需要更新为失效的分销订单ID
        $invalidDistributionOrderIds = [];
        // 按订单ID分组有效的分销订单
        $validDistributionOrders = [];

        // 直接处理每个分销订单
        foreach ($allDistributionOrders as $distributionOrder) {
            $afterSale = $afterSalesByOrderGoodsId[$distributionOrder['order_goods_id']] ?? null;

            if (!$afterSale) {
                // 没有售后记录，可以结算
                $validDistributionOrders[] = $distributionOrder;
                continue;
            }
            
            if (!$distributionOrder['hf_seq_id']) {
                Log::write('汇付订单没有保存hf_seq_id', 'error');
                Log::write($distributionOrder);
                continue;
            }

            // 有售后订单记录且状态在$no_settlement中的不结算分销订单
            // 售后情况不明不结算
            if (in_array($afterSale['status'], AfterSale::$no_settlement)) {
                continue;
            }

            // 分销订单更新为已失效不结算
            if (in_array($afterSale['status'], AfterSale::$set_fail)) {
                $invalidDistributionOrderIds[] = $distributionOrder['id'];
                continue;
            }
            
            // 其他情况可以结算
            $validDistributionOrders[] = $distributionOrder;
        }
        
        return [
            'valid' => $validDistributionOrders, 
            'invalid' => $invalidDistributionOrderIds
        ];
    }

    /**
     * 完成分销订单的分账操作
     * 
     * @param array $distributionOrderGoods 分销订单商品信息
     * @return bool 操作结果
     */
    public static function completeDistributionOrderSettle(array $distributionOrderGoods): bool
    {
        $time = time();
        $orderInfo = Order::where('id', $distributionOrderGoods['order_id'])
            ->field('order_sn,transaction_id,pay_way,hf_seq_id')
            ->find();
        $userInfo = User::where('id', $distributionOrderGoods['user_id'])
            ->field('id,earnings,partner_earnings')
            ->find();
            
        // 增加用户佣金
        $userInfo['earnings'] = money($userInfo['earnings'] + $distributionOrderGoods['money']);
        $userInfo['update_time'] = $time;
        $userInfo->save();

        // 记录流水
        AccountLog::create([
            'log_sn' => createSn('account_log', 'log_sn', '', 4),
            'user_id' => $distributionOrderGoods['user_id'],
            'source_type' => AccountLog::distribution_inc_earnings,
            'source_id' => $distributionOrderGoods['order_id'],
            'source_sn' => $orderInfo['order_sn'],
            'change_amount' => $distributionOrderGoods['money'],
            'left_amount' => money($userInfo['earnings'] + $userInfo['partner_earnings']),
            'change_type' => 1,
            'remark' => '分销佣金增加',
            'transaction_id' => $orderInfo['transaction_id'],
            'pay_way' => $orderInfo['pay_way'],
        ]);
        
        // 更新分销订单状态
        DistributionOrderGoods::update([
            'status' => DistributionOrderGoodsEnum::STATUS_SUCCESS,
            'update_time' => $time,
            'settlement_time' => $time
        ], ['id' => $distributionOrderGoods['id']]);

        // 确认订单的分账完成
        // 查询是否还有未完成分账的分销订单
        $pendingDistributionOrders = DistributionOrderGoods::where([
            ['order_id', '=', $distributionOrderGoods['order_id']],
            ['status', '=', DistributionOrderGoodsEnum::STATUS_WAIT_HANDLE],
            ['money', '>', 0]
        ])->count();

        // 更新订单分销佣金状态
        if ($pendingDistributionOrders == 0) {
            // 没有待处理的分销订单，表示全部完成
            $distributionMoneyStatus = Order::DISTRIBUTION_MONEY_STATUS_DONE;
        } else {
            // 还有待处理的分销订单，表示部分完成
            $distributionMoneyStatus = Order::DISTRIBUTION_MONEY_STATUS_PART;
        }

        // 更新订单分销佣金
        $orderModel = Order::findOrEmpty($distributionOrderGoods['order_id']);
        $orderModel->distribution_money = $orderModel->distribution_money + $distributionOrderGoods['money'];
        $orderModel->distribution_money_status = $distributionMoneyStatus;
        $orderModel->update_time = $time;
        $orderModel->save();
        
        return true;
    }

    /**
     * 获取订单平台服务费以及货款未结算的订单
     * 
     * 处理流程：
     * 1) 获取系统配置的可结算时间
     * 2) 查询可以结算的销售订单 Order 列表
     *    查询条件：
     *    - 支付方式为汇付微信支付或汇付支付宝支付 (pay_way in [HFDG_WECHAT, HFDG_ALIPAY])
     *    - 支付状态为已支付 (pay_status = ISPAID)
     *    - 订单状态为已完成 (order_status = STATUS_FINISH)
     *    - 平台服务费大于0 (platform_commission > 0)
     *    - 汇付支付正常回调 (hf_seq_id > 0)
     *    - 订单未删除 (del = 0)
     *    - 评价时间在可结算时间范围内 (all_comment_time between [1, maxCommentTime])
     *    - 退款状态为未退款或部分退款 (refund_status in [0, 1])
     * 3) 排除已完整结算的订单，存在全部/不分退款的销售订单
     * 4) 排除有暂不结算售后状态的订单
     * 
     * @return array 可结算的订单列表
     */
    public static function getOrderPlatformCommissionOrderLists(): array
    {
        // 1、获取系统配置的可结算时间
        $time = time();
        $afterSaleTime = ConfigServer::get('distribution', 'settlement_days', 7);
        $maxCommentTime = $time - intval($afterSaleTime * 24 * 60 * 60);

        // 2、查询可以结算的销售订单 Order 列表
        $orderWhere = [
            ['o.pay_way', 'in', [PayEnum::HFDG_WECHAT, PayEnum::HFDG_ALIPAY]], // 汇付支付方式
            ['o.pay_status', '=', PayEnum::ISPAID], // 已支付
            ['o.order_status', '=', Order::STATUS_FINISH], // 已完成
            ['o.refund_status', 'in', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART]], // 未退款或部分退款
            ['o.platform_commission', '>', 0], // 有平台服务费
            ['o.hf_seq_id', '>', 0], // 汇付支付正常回调
            ['o.del', '=', 0], // 未删除
            ['o.platform_commission_status', '=', 0], // 未结算或部分结算
        ];

        // 排除已完整结算的订单，存在全部/不分退款的销售订单
        $orderList = Order::alias('o')
            ->where($orderWhere)
            ->whereTime('o.all_comment_time', 'between', [1, $maxCommentTime]) // 评价时间在1到max_comment_time之间
            ->field('o.*')
            ->limit(1) // 每次处理100个订单
            ->select();
            
        if (empty($orderList)) {
            return [];
        }
        $orderListArr = $orderList->toArray();
        $orderIdArr = array_column($orderListArr, 'id');
        // 有售后的订单id
        $noSettlementOrderIdArr = AfterSale::where('order_id', 'in', $orderIdArr)
            ->where('del', 0)
            ->where('status', 'in', AfterSale::$no_settlement) // 暂不结算的售后单
            ->column('order_id');

        $validOrderList = [];
        foreach ($orderList as $order) {
            //会不会有???
            if ($noSettlementOrderIdArr && in_array($order['id'], $noSettlementOrderIdArr)) {
                //有售后订单id不结算 退款之后直接更改状态
                // $order->platform_commission_status =3;
                // $order->save();
                continue;
            }
            $validOrderList[] = $order;
        }

        return $validOrderList;
    }
}
