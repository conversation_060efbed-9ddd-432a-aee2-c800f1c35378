<?php

namespace app\api\validate;

use app\common\basics\Validate;
use app\common\server\ConfigServer;
use app\common\model\user\User;

/**
 * Class WithdrawValidate
 * @package app\api\validate
 */
class WithdrawValidate extends Validate
{
    protected $rule = [
        'id' => 'require', //参数缺失
        'type' => 'require|in:1,2,3,4,5',//提现类型
//        'pay_way'=> 'require|in:1,55',
        'money' => 'require|checkMoney',//提现佣金
        'account' => 'requireIf:type,3|requireIf:type,4|requireIf:type,5',//账户类型
        'real_name' => 'requireIf:type,3|requireIf:type,4|requireIf:type,5|chs',//真实姓名
        'money_qr_code' => 'requireIf:type,4|requireIf:type,3',//收款码
        'id_front'=> 'requireIf:type,5',//身份证正面
        'id_back'=> 'requireIf:type,5',//身份证正面
        'id_number'=> 'requireIf:type,3|requireIf:type,4|requireIf:type,5',//身份证号
        'mobile'=> 'require|mobile',//手机号
        'bank' => 'requireIf:type,5', // 提现银行
        'subbank' => 'requireIf:type,5', // 银行支行
    ];

    protected $message = [
        'id.require' => '参数缺失',
        'type.require' => '参数错误',
        'type.in' => '提现类型错误',
//        'pay_way.require' => '未获取支付方式',
//        'pay_way.in' => '支付方式错误',
        'money.require' => '参数错误',
        'account.requireIf' => '请填写账号',
        'id_front.requireIf' => '请上传身份证正面',
        'id_back.requireIf' => '请上传身份证反面',
        'id_number.requireIf' => '请填写身份证号',
        'mobile.requireIf' => '请填写手机号',
        'mobile.mobile' => '请填写正确的手机号',
        'real_name.requireIf' => '请填写真实姓名',
        'real_name.chs' => '请填写真实姓名',
        'money_qr_code.requireIf' => '请上传收款码',
        'bank.requireIf' => '请填写提现银行',
        'subbank.requireIf' => '请填写银行支行',
    ];

    /**
     * @notes 申请提现
     * @return WithdrawValidate
     * <AUTHOR>
     * @date 2021/7/13 6:30 下午
     */
    public function sceneApply()
    {

        return $this->only(['type', 'money', 'account', 'real_name', 'money_qr_code', 'bank', 'subbank', 'id_front', 'id_back', 'id_number', 'mobile', 'pay_way']);
    }

    /**
     * @notes 申请详情
     * @return WithdrawValidate
     * <AUTHOR>
     * @date 2021/7/13 6:30 下午
     */
    public function sceneInfo()
    {

        return $this->only(['id']);
    }

    /**
     * @notes 提现佣金验证
     * @param $value
     * @param $rule
     * @param array $data
     * @return bool|string
     * <AUTHOR>
     * @date 2021/7/13 6:30 下午
     */
    protected function checkMoney($value, $rule, $data = [])
    {

        $able_withdraw = User::where('id', $data['user_id'])->value('earnings');
        if ($value > $able_withdraw) {
            return '可提现金额不足';
        }

        //1.最低提现金额
        $min_withdraw = ConfigServer::get('withdraw', 'min_withdraw', 0);
        if ($value < $min_withdraw) {
            return '最低提现' . $min_withdraw . '元';
        }

        //2,最高提现金额
        $max_withdraw = ConfigServer::get('withdraw', 'max_withdraw', 0);
        if ($value > $max_withdraw) {
            return '最高提现' . $max_withdraw . '元';
        }
        if ($value >= 5000){
            if ($data['type'] != 5){
                return '金额5000元以上，请选择银行卡提现';
            }
        }else{
            if ($data['type'] == 5){
                return '金额5000元以下，请选择支付宝或微信';
            }
        }


        return true;
    }
}