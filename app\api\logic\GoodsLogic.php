<?php


namespace app\api\logic;

use app\common\model\community\CommunityArticleGoods;
use app\common\model\community\CommunityArticleImage;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionLevel;
use app\common\model\goods\GoodsCategory;
use app\common\model\goods\GoodsQualification;
use app\common\model\goods\GoodsShow;
use app\common\model\goods\GoodsTags;
use app\common\model\goods\Show;
use app\common\model\seckill\SeckillTime;
use app\common\model\user\User;
use app\common\basics\Logic;
use app\common\enum\FootprintEnum;
use app\common\model\distribution\DistributionGoods;
use app\common\model\goods\Goods;
use app\common\model\goods\GoodsCollect;
use app\common\model\goods\GoodsClick;
use app\common\model\goods\GoodsSpec;
use app\common\model\goods\GoodsComment;
use app\common\model\goods\GoodsCommentImage;
use app\common\model\SearchRecord;
use app\common\enum\GoodsEnum;
use app\common\model\seckill\SeckillGoods;
use app\common\model\shop\Shop;
use app\common\model\team\TeamActivity;
use app\common\model\team\TeamFound;
use app\common\model\team\TeamGoods;
use app\common\model\user\UserLevel;
use app\common\server\ConfigServer;
use app\common\server\UrlServer;
use app\shop\controller\goods\Qualification;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Db;

class GoodsLogic extends Logic
{
    /**
     * 商品详情
     */
    public static function getGoodsDetail($goodsId, $userId, $distribution)
    {
        //获取用户折扣
        $discount = 10;
        if($userId){
            $user = User::where('id', $userId)->find();
            if($user && isset($user['level'])){
                $user_discount = UserLevel::where('id', $user['level'])->value('discount');
                if($user_discount && $user_discount > 0 && $user_discount <= 10){
                    $discount = $user_discount;
                }
            }
        }

        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            'del' => GoodsEnum::DEL_NORMAL, // 未删除
            'status' => GoodsEnum::STATUS_SHELVES, // 上架中
            'audit_status' => GoodsEnum::AUDIT_STATUS_OK, // 审核通过
        ];

        $goodsDetail = Goods::with(['goods_image', 'goods_item', 'shop'])
            ->field('id,type,name,image,video,remark,content,market_price,min_price,max_price,is_show_stock,stock,sales_actual,sales_virtual,clicks,clicks_virtual,shop_id,poster,goods_tags,goods_attr,qualification_id')
            ->where($onSaleWhere)
            ->where('id', $goodsId)
            ->findOrEmpty();

        if ($goodsDetail->isEmpty()) {
            self::$error = '商品已下架';
            return false;
        }
        $goodsDetail['goods_tags'] = self::goodsDetailTags($goodsDetail['goods_tags']);
        $goodsDetail['goods_attr'] = self::goodsAttrFormat($goodsDetail['goods_attr']);
        // 是否返回佣金金额
        $is_show_earnings = ConfigServer::get('distribution', 'is_show_earnings', 0);
        if ($is_show_earnings && isset($distribution['user_id']) && $distribution['user_id'] === $userId) {
            if ($distribution && $distribution['is_distribution'] && !$distribution['is_freeze']){
                $distribution_goods_arr = DistributionGoods::field('goods_id,first_ratio,second_ratio,level_id,item_id,rule')->where('goods_id', '=', $goodsId)->findOrEmpty()->toArray();
                if ($distribution_goods_arr){
                    $goodsDetail['earnings'] = floormoney($distribution_goods_arr['first_ratio'] * $goodsDetail['min_price'] / 100);
                    $goodsDetail['is_show_earnings'] = $goodsDetail['earnings'] >0 ? 1 : 0;
                }
            }
        }
        Db::startTrans();
        try{
            // 轮播图加域名
            foreach($goodsDetail['goods_image'] as $k=>$item) {
                $goodsDetail['goods_image'][$k] = empty($item['uri']) ? '' : UrlServer::getFileUrl($item['uri']);
//                $goodsDetail['goods_image'][$k] = imgOssCompress($goodsDetail['goods_image'][$k], 'q/98');
            }
            // 会员价
            $goodsDetail['member_price'] = 0;
            // 会员价数组
            $member_price = [];
            foreach ($goodsDetail['goods_item'] as &$goods_item) {
                $is_member = Goods::where('id',$goods_item['goods_id'])->value('is_member');
                $goods_item['is_member'] = $is_member;
                if($is_member == 1 && $discount && $userId){
                    $goods_item['member_price'] = round($goods_item['price']* $discount/10,2);
                    $goodsDetail['member_price'] =  round($goods_item['price']* $discount/10,2);
                    $member_price[] = $goodsDetail['member_price'];
                }
                // 规格图片处理
                $goods_item['image'] = empty($goods_item['image']) ? $goodsDetail['image'] : $goods_item['image'];
//                $goods_item['image'] = imgOssPreview($goods_item['image']);
            }
            $goodsDetail['qualification_info'] = GoodsQualification::where('id', '=', $goodsDetail['qualification_id'])->findOrEmpty();
            if (isset($goodsDetail['qualification_info']) && $goodsDetail['qualification_info'] && isset($goodsDetail['qualification_info']['images']) && $goodsDetail['qualification_info']['images']){
                $goodsDetail['qualification_info']['images'] = explode(',', $goodsDetail['qualification_info']['images']);
            }
            !empty($member_price) && $goodsDetail['member_price'] = min($member_price);
            $goodsDetail['video'] = $goodsDetail['video'] ? UrlServer::getFileUrl($goodsDetail['video']) : '';
            $goodsDetail['clicks'] = $goodsDetail['clicks'] + 1;
            $goodsDetail->save();
            // 转数组
            $goodsDetailArr = $goodsDetail->toArray();
            //视频路径替换
            $goodsDetailArr['content'] = UrlServer::relative_video_to_absolute($goodsDetailArr['content']);
            $goodsDetailArr['poster'] = !empty($goodsDetailArr['poster']) ? UrlServer::getFileUrl($goodsDetailArr['poster']) : '';

            //增加商品所在店铺信息
            $goodsDetailArr = self::getGoodsShopInfo($goodsDetailArr);

            // 总销量 = 实际销量 + 虚拟销量
            $goodsDetailArr['sales_sum'] = $goodsDetailArr['sales_actual'] + $goodsDetailArr['sales_virtual'];
            // 标识活动信息
            $goodsDetailArr['activity'] = [
                'type' => 0,
                'is_start' => 1,
                'type_desc' => '普通商品'
            ];
            // 检查商品是否在参与活动，替换商品价格
            $goodsDetailArr = self::checkActivity($goodsDetailArr);
            // 是否收藏
            $goodsDetailArr['is_collect'] = 0;
            if($userId) { // 非游客
                $goodsCollect = GoodsCollect::where([
                    'user_id' => $userId,
                    'goods_id' => $goodsId
                ])->findOrEmpty();
                if(!$goodsCollect->isEmpty()) {
                    $goodsDetailArr['is_collect'] = $goodsCollect->status ? 1 : 0;
                }
            }
            // 规格项及规格值信息
            $goodsDetailArr['goods_spec'] = GoodsSpec::with('spec_value')
                ->where('goods_id', $goodsId)->select();
            // 商品评价
            $commentCategory = GoodsCommentLogic::category(['goods_id'=>$goodsId]);
            $goodsDetailArr['comment']['percent'] = $commentCategory['percent'];

            $all_comment = Db::name('goods_comment')->where(['goods_id' => $goodsId])->sum('goods_comment');
            $goods_comment_count = Db::name('goods_comment')->where(['goods_id' => $goodsId])->count('id');
            if($goods_comment_count){
                $goods_comment = round($all_comment / $goods_comment_count,2);
                $goodsDetailArr['comment']['goods_comment'] = $goods_comment;
            }else{
                $goodsDetailArr['comment']['goods_comment'] = 0;
            }
            // 最新一条评论
            $one = GoodsComment::alias('gc')
                ->field('gc.id,gc.goods_comment,gc.create_time,gc.comment,u.avatar,u.nickname,g.name as goods_name')
                ->leftJoin('user u', 'u.id=gc.user_id')
                ->leftJoin('goods g', 'g.id=gc.goods_id')
                ->where([
                    ['gc.goods_id', '=', $goodsId],
                    ['gc.del', '=', 0],
                    ['gc.status', '=', 1],
                ])
                ->order('create_time', 'desc')
                ->findOrEmpty();
            if($one->isEmpty()) {
                $one = [];
            }else {
                $one = $one->toArray();
                // 头像
                $one['avatar'] = UrlServer::getFileUrl($one['avatar']);
                // 图片评价
                $one['image'] = GoodsCommentImage::where('goods_comment_id', $one['id'])->column('uri');
                foreach($one['image'] as $subKey => $subItem) {
                    $one['image'][$subKey] = UrlServer::getFileUrl($subItem);
                }
            }
            $goodsDetailArr['comment']['one'] = $one;

            // 判断是否是拼团商品
            $teamActivity = (new TeamActivity())
                ->field(['id,people_num,team_max_price,team_min_price,sales_volume,activity_end_time,share_title,share_intro'])
                ->where([
                    ['goods_id', '=', $goodsId],
                    ['audit', '=', 1],
                    ['status', '=', 1],
                    ['del', '=', 0],
                    ['activity_start_time', '<=', time()],
                    ['activity_end_time', '>=', time()]
            ])->findOrEmpty()->toArray();

            if ($teamActivity) {
                $teamFound = (new TeamFound())->alias('TF')
                    ->field(['TF.*', 'U.nickname,U.avatar'])
                    ->limit(8)
                    ->order('id desc')
                    ->where('TF.team_activity_id', '=', $teamActivity['id'])
                    ->where('TF.people','exp',' > TF.join ')
                    ->where([
                        ['status', '=', 0],
                        ['invalid_time', '>=', time()]
                    ])->join('user U', 'U.id=TF.user_id')
                      ->select()->toArray();

                foreach ($teamFound as &$found) {
                    unset($found['shop_id']);
                    unset($found['team_sn']);
                    unset($found['goods_snap']);
                    unset($found['team_end_time']);
                    $found['avatar'] = UrlServer::getFileUrl($found['avatar']);
                    $found['surplus_time'] = intval($found['invalid_time'] - time());
                }

                $teamActivity['share_title'] = !empty($teamActivity['share_title']) ? $teamActivity['share_title'] : $goodsDetailArr['name'];
                $teamActivity['share_intro'] = !empty($teamActivity['share_intro']) ? $teamActivity['share_intro'] : $goodsDetailArr['remark'];

                $goodsDetailArr['activity'] = ['type'=>2, 'is_start'=>1, 'type_desc'=>'拼团商品', 'info'=>$teamActivity, 'found'=>$teamFound];
                $teamGoods = (new TeamGoods())->where(['team_id'=>$teamActivity['id']])->select()->toArray();
                foreach ($goodsDetailArr['goods_item'] as &$item) {
                    foreach ($teamGoods as $team) {
                        if ($item['id'] === $team['item_id']) {
                            $item['team_price'] = $team['team_price'];
                        }
                    }
                }
            }

            // 虚拟浏览量
            $goodsDetailArr['clicks'] += $goodsDetailArr['clicks_virtual'];
            //浏览量增加


            //商品购买用户列表
//            $goodsDetailArr['recent_buyer_list'] = OrderLogic::getGoodsRecentBuyerList($goodsDetailArr['id']);


//            // 新增点击记录
            GoodsClick::create([
                'shop_id' => $goodsDetailArr['shop_id'],
                'user_id' => $userId,
                'goods_id' => $goodsId,
                'create_time' => time()
            ]);

            // 记录访问足迹
            event('Footprint', [
                'type'    => FootprintEnum::BROWSE_GOODS,
                'user_id' => $userId,
                'foreign_id' => $goodsId
            ]);

            Db::commit();
            return $goodsDetailArr;
        }catch(\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 热销榜单
     */
    public static  function getHotList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        $order = [
            'sales_total' => 'desc', // 实际销量+虚拟销量倒序
            'sales_actual' => 'desc', // 实际销量倒序
            'id' => 'desc'
        ];

        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 商品列表
     */
    public static function getGoodsList($get)
    {
        // 销售中商品：未删除/审核通过/已上架
        $where = [
            ['del', '=', GoodsEnum::DEL_NORMAL],  // 未删除
            ['status', '=', GoodsEnum::STATUS_SHELVES], // 上架中
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK], // 审核通过
        ];
        $order = [
            'sort' => 'asc', // 商品权重，数字越小权重越大
            'id' => 'desc'
        ];
        return self::getGoodsListTemplate($where, $order, $get);
    }

    /**
     * 商品列表模板
     * 作用：代码复用
     */
    public static function getGoodsListTemplate($where, $order, $get)
    {
        if (!empty(self::filterShopsIds())) {
            // 过滤已删除、已冻结、已暂停营业、已到期的店铺
            $where[] = ['shop_id', 'not in', self::filterShopsIds()];
        }
//        $show_type = 1;
        // 平台分类
        $platform_cate_id =0;
        if(isset($get['platform_cate_id']) && !empty($get['platform_cate_id']) && filter_var($get['platform_cate_id'], FILTER_VALIDATE_INT)) {
            $platform_cate_id=$get['platform_cate_id'];
            if (is_numeric($platform_cate_id)){
                $is_show = GoodsCategory::where(['id'=>$platform_cate_id])->value('is_show');
//                if ($is_show == 0){
//                    $show_type = 2;
//                }
            }
            $where[] = ['first_cate_id|second_cate_id|third_cate_id', '=', $get['platform_cate_id']];
        }else{
            if (!isset($get['keyword'])){
                $where[] = ['first_cate_id', '<>', 119];//todo 过滤指定不显示的分类
            }
        }
        // 品牌
        if(isset($get['brand_id']) && !empty($get['brand_id']) && filter_var($get['brand_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['brand_id', '=', $get['brand_id']];
        }
        $whereOr =[];
        // 关键词
        if(isset($get['keyword']) && !empty($get['keyword'])) {
            $keywords = trim($get['keyword']);
            $keywords = replaceNullTo($keywords);
            if (!strpos($keywords, ' ') === false){
                $keywords_arr = explode(' ', $keywords);
                if ($keywords_arr){
                    foreach ($keywords_arr as $item){
                        $whereOr[] = ['name','like','%'.$item.'%'];
                    }
                }
            }else{
                $where[] = ['name','like','%'.$keywords.'%'];
            }
            if($get['user_id']) { // 记录关键词
                self::recordKeyword(trim($get['keyword']), $get['user_id']);
            }
        }

        if(isset($get['qualification'])) {
            $where[] = ['qualification_id', '>', '0'];
        }

        // 店铺id
        if(isset($get['shop_id']) && !empty($get['shop_id']) && filter_var($get['shop_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_id', '=', $get['shop_id']];
        }

        // 店铺分类
        if(isset($get['shop_cate_id']) && !empty($get['shop_cate_id']) && filter_var($get['shop_cate_id'], FILTER_VALIDATE_INT)) {
            $where[] = ['shop_cate_id', '=', $get['shop_cate_id']];
        }

        // 销量排序(实际销量 + 虚拟销量)
        if(isset($get['sort_by_sales']) && !empty($get['sort_by_sales'])) {
            $elt = ['sales_total'=> trim($get['sort_by_sales'])];
            $order = array_merge($elt, $order);
        }

        // 价格排序
        if(isset($get['sort_by_price']) && !empty($get['sort_by_price'])) {
            $elt = ['min_price'=> trim($get['sort_by_price'])];
            $order = array_merge($elt, $order);
        }

        if(isset($get['sort_by_collect']) && !empty($get['sort_by_collect'])) {
            $elt = ['collect_count'=> trim($get['sort_by_collect'])];
            $order = array_merge($elt, $order);
        }

        // 新品排序
        if(isset($get['sort_by_create']) && !empty($get['sort_by_create'])) {
            $elt = ['onsale_time'=> trim($get['sort_by_create'])];
            $order = array_merge($elt, $order);
        }
        // 更新时间排序
        if(isset($get['sort_by_update']) && !empty($get['sort_by_update'])) {
            $elt = ['update_time'=> trim($get['sort_by_update'])];
            $order = array_merge($elt, $order);
        }
        $field = 'id,image,name,remark,min_price,market_price,sales_actual,first_cate_id,
        second_cate_id,third_cate_id,sort,sort_weight,brand_id,shop_id,sales_virtual,
        (sales_actual + sales_virtual) as sales_total,onsale_time,collect_count';

        $list = Goods::with(['shop'])
            ->field($field)
            ->where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->order($order)
            ->page($get['page_no'], $get['page_size'])
            ->select();

        $goods_id_arr=[];
        foreach ($list as &$item) {
            $goods_id_arr[] = $item['id'];
            $item['shop_type'] = $item['shop']['type'];
            unset($item['shop']);
            $item['is_collect'] = 0;
            $item['image'] = imgOssPreview($item['image']);
        }
        if (isset($get['user_id']) && $get['user_id']){
            $collected_list = GoodsCollect::where('user_id', $get['user_id'])
                ->where('goods_id', 'in', $goods_id_arr)
                ->column('status,goods_id');
            if ($collected_list){
                $collected_list = array_column($collected_list, 'status', 'goods_id');
                foreach ($list as &$item) {
                    $item['is_collect'] = isset($collected_list[$item['id']]) ? $collected_list[$item['id']] : 0;
                }
            }

//            if (isset($get['with_commission']) && $get['with_commission'] == 1){
//                $list =  self::getNormalGoodsCommission($goods_id_arr, $list);
//            }

        }



        $count = Goods::where($where)->count();

        $list = $list ? $list->toArray() : [];
        $more = is_more($count, $get['page_no'], $get['page_size']);
        $platform_cate =[];
        if ($platform_cate_id){
            $platform_cate = GoodsCategory::field('id,name,image,bg_image')->where(['id' => $platform_cate_id])->where('del', '=', 0)->find();
            if ($platform_cate){
                $platform_cate['image'] = $platform_cate['image'] ? UrlServer::getFileUrl($platform_cate['image']) : '';
                $platform_cate['bg_image'] = $platform_cate['bg_image'] ? UrlServer::getFileUrl($platform_cate['bg_image']) : '';
            }
        }

        $data = [
            'lists'         => $list,
            'platform_cate'=>$platform_cate,
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * 获取商品佣金, 不区分用户角色等级
     * @param $user_id
     * @param $goods_id_arr
     * @param $goods_list
     */
    public static function getNormalGoodsCommission($goods_id_arr, $goods_list, $distribution_info=[]){
        if (!$distribution_info || $distribution_info['is_distribution'] != 1){
            return $goods_list;
        }
        $distribution_goods_arr = DistributionGoods::where([['goods_id', 'in', $goods_id_arr]])->column('goods_id,first_ratio,second_ratio,level_id,item_id,rule','goods_id');
        foreach ($goods_list as &$item){
            $item['commission_rate1'] = $distribution_goods_arr[$item['id']]['first_ratio'] ?? 0;
//            $item['commission_rate2'] = $distribution_goods_arr[$item['id']]['second_ratio'] ?? 0;
            $item['commission1'] = floormoney($item['commission_rate1'] * $item['min_price'] / 100);
//            $item['commission2'] = floormoney($item['commission_rate2'] * $item['min_price'] / 100);
            if($distribution_info['invite_open_shop'] == 1){
                $item['commission_rate2'] = $distribution_goods_arr[$item['id']]['second_ratio'] ?? 0;
                $item['commission2'] = floormoney($item['commission_rate2'] * $item['min_price'] / 100);
            }else{
                $item['commission_rate2'] = 0;
                $item['commission2'] = 0;
            }
        }
        return $goods_list;
    }


    /**
     * 获取当前用户 每款商品能获得多少佣金
     * @return void
     */
    public static function getUserGoodsCommission($user_id, $goods_id_arr, $goods_list)
    {
        $distribution = Distribution::where(['user_id' => $user_id, 'is_distribution'=> 1, 'is_freeze'=> 0])->findOrEmpty()->toArray();
        if (!$distribution){
            return $goods_list;
        }
        $distribution_level = DistributionLevel::where('id', '=', $distribution['level_id'])->find()->toArray();
        if($distribution['distribution_commission_method'] == 1){
            foreach ($goods_list as &$item){
                $item['commission_rate1'] = $distribution_level['first_ratio'] ?? 0;
                $item['commission1'] = money($item['commission_rate1'] * $item['min_price'] / 100);
                if ($distribution['invite_open_shop'] == 1){
                    $item['commission_rate2'] = $distribution_level['second_ratio'] ?? 0;
                    $item['commission2'] = money($item['commission_rate2'] * $item['min_price'] / 100);
                }
            }
        }
        /**
         * 按照商品分佣
         */
        if ($distribution['distribution_commission_method'] == 2){
            $distribution_goods_arr = DistributionGoods::where([['goods_id', 'in', $goods_id_arr]])->column('goods_id,first_ratio,second_ratio,level_id,item_id,rule','goods_id');
            foreach ($goods_list as &$item){
                if (isset($distribution_goods_arr[$item['id']]) && $distribution_goods_arr[$item['id']]['rule'] == 2){
                    //商品中按照商品分佣
                    $item['commission_rate1'] = $distribution_goods_arr[$item['id']]['first_ratio'] ?? 0;
                    $item['commission1'] = money($item['commission_rate1'] * $item['min_price'] / 100);
                    if ($distribution['invite_open_shop'] == 1){
                        $item['commission_rate2'] = $distribution_goods_arr[$item['id']]['second_ratio'] ?? 0;
                        $item['commission2'] = money($item['commission_rate2'] * $item['min_price'] / 100);
                    }
                }else{
                    //商品中设置按照等级
                    $item['commission_rate1'] = $distribution_level['first_ratio'] ?? 0;
                    $item['commission1'] = money($item['commission_rate1'] * $item['min_price'] / 100);
                    if ($distribution['invite_open_shop'] == 1){
                        $item['commission_rate2'] = $distribution_level['second_ratio'] ?? 0;
                        $item['commission2'] = money($item['commission_rate2'] * $item['min_price'] / 100);
                    }
                }
            }
        }
        return $goods_list;
    }

    /**
     * 根据商品栏目获取商品列表
     */
    public static function getGoodsListByColumnId($columnId, $page_no, $page_size)
    {
        $cache_key = 'get_goods_list_by_column_id';
        $cache_uri = $cache_key.'_'.$columnId.'_'.$page_no.'_'.$page_size;
        $data_list = Cache::get($cache_uri);
        if ($data_list){
            return $data_list;
        }
        // 销售中商品：未删除/审核通过/已上架
        $onSaleWhere = [
            ['del', '=', GoodsEnum::DEL_NORMAL],
            ['status', '=', GoodsEnum::STATUS_SHELVES],
            ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK],
        ];

        if (!empty(self::filterShopsIds())) {
            // 过滤已删除、已冻结、已暂停营业、已到期的店铺
            $onSaleWhere[] = ['shop_id', 'not in', self::filterShopsIds()];
        }

        $order = [
            'sort' => 'asc', // 数字越小，权重越大
            'sales_actual' => 'desc',
            'id' => 'desc'
        ];

        $list = Goods::field('id,name,image,remark,market_price,min_price,sales_actual,column_ids,sort,sort_weight,sales_virtual,(sales_actual + sales_virtual) as sales_total')
            ->where($onSaleWhere)
            ->whereFindInSet('column_ids', $columnId)
            ->order($order)
            ->page($page_no, $page_size)
            ->select();

        $count = Goods::where($onSaleWhere)
            ->whereFindInSet('column_ids', $columnId)
            ->count();

        $list = $list ? $list->toArray() : [];

        $more = is_more($count, $page_no, $page_size);

        $data = [
            'lists'          => $list,
            'page_no'       => $page_no,
            'page_size'     => $page_size,
            'count'         => $count,
            'more'          => $more
        ];
        Cache::set($cache_uri, $data, Config::get('project.expire_time.'.$cache_key, 60));
        return $data;
    }

    /**
     * @notes 获取已删除、已冻结、已暂停营业、已到期店铺的id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/20 14:29
     */
    public static function filterShopsIds()
    {
        // 已删除、已冻结、已暂停营业的店铺
        $invalidShops = Shop::field('id,name')->whereOr([
            ['del', '=', 1], // 已删除
            ['is_freeze', '=', 1], // 已冻结
            ['is_run', '=', 0] // 暂停营业
        ])->select()->toArray();

        // 已过期的店铺
        $expiredShops = Shop::field('id,name')->where([
            ['expire_time', '<>', 0],
            ['expire_time', '<=', time()],
        ])->select()->toArray();

        $filterShops = array_merge($invalidShops, $expiredShops);
        $filterShopsIds = array_column($filterShops, 'id');
        return $filterShopsIds;
    }

    /**
     * 记录关键词
     */
    public static function recordKeyword($keyword, $user_id)
    {
        $record = SearchRecord::where(['user_id'=>$user_id,'keyword'=>$keyword,'del'=>0])->find();
        if($record){
            // 有该关键词记录, 更新
            return SearchRecord::where(['id'=>$record['id']])->update(['count'=>Db::raw('count+1'),'update_time'=>time()]);
        }
        // 无该关键词记录 > 新增
        return SearchRecord::create([
            'user_id'=>$user_id,
            'keyword'=>$keyword,
            'count' => 1,
            'update_time' => time(),
            'del' => 0
        ]);
    }


    /**
     * 检查是否参与活动
     * 与旧版相比: 只要参加了 就返回参加了, 不再区分是否是已经开始,
     *  同时返回是否开始, 是否开始不能只依据世时间判断, 还有开始时间段的问题,
     *  比如限定1日到5日的 12点--15点秒杀开始秒杀, 除了判定是否是12点--15点, 还要判断是否是1日到5日
     * @return void
     */
    public static function checkActivity($goods){
        $seckill_goods = SeckillGoods::where([
            ['del', '=', 0],
            ['goods_id', '=', $goods['id']],
            ['review_status', '=', 1],
        ])->select()->toArray();
        if (!$seckill_goods){
            return $goods;
        }
        $seckill_time = SeckillTime::field('id,start_time,end_time')->where('del', '=', 0)->where('id', '=', $seckill_goods[0]['seckill_id'])->find()->toArray();
        if (!$seckill_time){
            return $goods;
        }
        $now = time();
        $start_date = $seckill_goods[0]['start_date'] ?? '';
        $start_time = $start_date ? strtotime($start_date) : 0;
        $end_date = $seckill_goods[0]['end_date'] ?? '';
        $end_time = $end_date ? strtotime($end_date) : 0;
        $goods['activity'] = [
            'type' => 1,
            'type_desc' => '秒杀商品',
            'period'=>$seckill_time,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'start_time' => strtotime($start_date.' '.$seckill_time['start_time']),
            'end_time' => strtotime($end_date.' '.$seckill_time['end_time']),
        ];
        $start_time = strtotime($start_date.' '.$seckill_time['start_time']);
        $end_time = strtotime($end_date.' '.$seckill_time['end_time']);
        if($start_time <= $now && $end_time >= $now) {//在秒杀时间内
            $goods['activity']['is_start'] = 1;
        }else if ($start_time > $now) {//秒杀未开始
            $goods['activity']['is_start'] = 2;
        } else{//秒杀已结束
            $goods['activity']['is_start'] = 0;
            $goods['activity']['type'] = 0;
        }
        //在秒杀时段内
        if($goods['activity']['is_start'] == 1) {
            $seckill_goods = array_column($seckill_goods, null, 'item_id');
            // 先将商品市场价换成原SKU最小价
            $goods['market_price'] = $goods['min_price'];
            // 替换活动价
            foreach($goods['goods_item'] as &$item) {
                // 商品价格替换为最小的秒杀价
                if($goods['min_price'] > $seckill_goods[$item['id']]['price']) {
                    $goods['min_price'] = $seckill_goods[$item['id']]['price'];
                }
                // 原市场价替换为原SKU售价
                $item['market_price'] = $item['price'];
                // SKU替换秒杀价
                $item['price'] = $seckill_goods[$item['id']]['price'];
            }
        }
        return $goods;
    }

    //检查商品是否正在参加活动
    public static function checkActivity_old($goods){
        // 获取正在秒杀的时段
        $seckill_time = SeckillGoodsLogic::getSeckillTimeIng();
        if($seckill_time === false) {
            // 不在秒杀时段，直接返回
            return $goods;
        }
        // 判断是否是秒杀中的商品
        $seckill_goods = SeckillGoods::where([
            ['del', '=', 0],
            ['seckill_id', '=', $seckill_time['id']],
            ['goods_id', '=', $goods['id']],
            ['review_status', '=', 1],
        ])->select()->toArray();
        if(!$seckill_goods) {
            // 不是秒杀商品
            return $goods;
        }
        // 判断参与日期是否包含今天
        $flag = false;
        $now = time();
        foreach($seckill_goods as $item) {
            $start_date_time = strtotime($item['start_date'].' 00:00:00');
            $end_date_time = strtotime($item['end_date'].' 00:00:00');
            if($start_date_time < $now && $end_date_time > $now) {
                $flag = true;
                // 获取该商品的秒杀信息
                $seckill_goods_info = SeckillGoods::where([
                    'goods_id' => $goods['id'],
                    'seckill_id' => $seckill_time['id'],
                    'start_date' => $item['start_date'],
                    'end_date' => $item['end_date'],
                ])->column('goods_id,item_id,price', 'item_id');
                break;
            }
        }
        if($flag === false) {
            // 参与日期不在今天
            return $goods;
        }
        // 确定是秒杀中的商品
        // 先将商品市场价换成原SKU最小价
        $goods['market_price'] = $goods['min_price'];
        // 替换活动价
        foreach($goods['goods_item'] as &$item) {
            // 商品价格替换为最小的秒杀价
            if($goods['min_price'] > $seckill_goods_info[$item['id']]['price']) {
                $goods['min_price'] = $seckill_goods_info[$item['id']]['price'];
            }
            // 原市场价替换为原SKU售价
            $item['market_price'] = $item['price'];
            // SKU替换秒杀价
            $item['price'] = $seckill_goods_info[$item['id']]['price'];
        }
        $today_date = date('Y-m-d');
        $goods['activity'] = [
            'type' => 1,
            'type_desc' => '秒杀商品',
            'end_time' => strtotime($today_date.' '.$seckill_time['end_time'])
        ];
        return $goods;

    }

    /**
     * @notes 获取商品分销信息
     * @param $goodsId
     * @param $userId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/9/6 18:48
     */
    public static function getDistribution($goodsId, $userId)
    {
        $earnings = 0;
        $goods = Goods::findOrEmpty($goodsId)->toArray();
        $distributionGoods = DistributionGoods::where('goods_id', $goodsId)->select()->toArray();
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 2) {
            foreach($distributionGoods as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }
        if(!empty($distributionGoods) && $distributionGoods[0]['is_distribution'] && $distributionGoods[0]['rule'] == 1) {
            $levels = DistributionLevel::select()->toArray();
            foreach($levels as $item) {
                $earnings = max($earnings, round($goods['max_price'] * $item['first_ratio'] / 100, 2));
                $earnings = max($earnings, round($goods['max_price'] * $item['second_ratio'] / 100, 2));
            }
        }

        // 详情页是否显示佣金
        $isShow = ConfigServer::get('distribution', 'is_show_earnings', 0);
        // 系统总分销开关
        $distributionOpen = ConfigServer::get('distribution', 'is_open', 0);
        // 商家信息-获取商家是否被禁用分销功能(is_distribution)
        $shop = Shop::findOrEmpty($goods['shop_id'])->toArray();

        if ($distributionOpen && $shop['is_distribution'] && $isShow) {
            //详情页佣金可见用户 0-全部用户 1-分销商
            $scope = ConfigServer::get('distribution', 'show_earnings_scope', 0);
            $user = Distribution::where(['user_id' => $userId])->findOrEmpty()->toArray();
            if ($scope && empty($user['is_distribution'])) {
                $isShow = 0;
            }
        } else {
            $isShow = 0;
        }
        
        return [
            'is_show' => $isShow,
            'earnings' => $earnings
        ];
    }

    /**
     * Notes: 兼容 带有空格的查询
     * Author: Darren
     * DateTime: 2023-06-01 14:42
     */
    public static function showlists__old($user_id, $get){
        $where[] = ['status', '=', GoodsEnum::STATUS_SHELVES];//上架
        $where[] = ['del', '=', GoodsEnum::DEL_NORMAL];
        $where[] = ['audit_status', '=', GoodsEnum::AUDIT_STATUS_OK];//审核通过
        $where[] = ['show_slogan','<>',''];
        $keywords = $get['show_slogan'] ?? '';
        $whereOr=[];
        if ($keywords){
            $keywords = trim($keywords);
            $keywords = replaceNullTo($keywords);
            if (!strpos($keywords, ' ') === false){
                $keywords_arr = explode(' ', $keywords);
                if ($keywords_arr){
                    foreach ($keywords_arr as $item){
                        $whereOr[] = ['show_slogan|name','like','%'.$item.'%'];
                    }
                }
            }else{
                $where[] = ['show_slogan|name','like','%'.$keywords.'%'];
            }
        }
        $lists = Goods::where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->page($get['page_no'], $get['page_size'])
            ->order('slogan_time desc')
            ->column('id,name,unit_id,remark,image,show_slogan,slogan_time,poster,image,video,poster', 'id');
        $count = Goods::where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->count();
        if($lists) {
            foreach ($lists as $goods_id=>$goods){
                $lists[$goods_id]['image'] = $lists[$goods_id]['image'] ? UrlServer::getFileUrl($lists[$goods_id]['image']) : '';
                if ($lists[$goods_id]['poster']){
                    $lists[$goods_id]['poster'] = $lists[$goods_id]['poster'] ? UrlServer::getFileUrl($lists[$goods_id]['poster']) : '';
                }else{
                    $lists[$goods_id]['poster'] = $lists[$goods_id]['image'];
                }
                unset($lists[$goods_id]['image']);
                if (!$lists[$goods_id]['poster']){
                    unset($lists[$goods_id]);
                }
                $lists[$goods_id]['video'] = $lists[$goods_id]['video'] ? UrlServer::getFileUrl($lists[$goods_id]['video']) : '';
//                $url_data=[
//                    'id'=>$goods_id,
//                    'type'=>1
//                ];
//                $lists[$goods_id]['url'] = ShareLogic::getWechatUrllink($user_id, $url_data);
            }
            $lists = array_values($lists);
        }else{
            $lists = [];
        }
        $new_list=[];
        if($lists){
            foreach ($lists as $k=>$val){
                $time_map = strtotime($val['slogan_time']);
                $slogan_time = date('Y-m', $time_map);
                $new_list[$slogan_time]['slogan_time'] = $slogan_time;
                $new_list[$slogan_time]['slogan_year'] = date('Y', $time_map);
                $new_list[$slogan_time]['slogan_month'] = date('m', $time_map);
                $new_list[$slogan_time]['slogan_day'] = date('d', $time_map);
                $new_list[$slogan_time]['data'][] = $val;
            }
        }
        $more = is_more($count, $get['page_no'], $get['page_size']);
        $data = [
            'lists'         => array_values($new_list),
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;
    }

    /**
     * Notes:
     * Author: Darren
     * DateTime: 2023-08-14 9:44
     */
    public static function goodsLists($get){
        $keywords = $get['goods_name'] ?? '';
        $where_goods[] =['del', '=', 0];
        $where_goods[] =['status', '=', 1];
        $whereOr=[];
        $search_goods_id_arr =[];
        if ($keywords){
            $keywords = trim($keywords);
            $keywords = replaceNullTo($keywords);
            if (!strpos($keywords, ' ') === false){
                $keywords_arr = explode(' ', $keywords);
                if ($keywords_arr){
                    foreach ($keywords_arr as $item){
                        $whereOr[] = ['name','like','%'.$item.'%'];
                    }
                }
            }else{
                $where_goods[] = ['name','like','%'.$keywords.'%'];
            }
            $search_goods_id_arr = Goods::where($where_goods)
                ->where(function ($query) use ($whereOr) {
                    if ($whereOr){
                        $query->whereOr($whereOr);
                    }
                })
                ->column('id');
            if (!$search_goods_id_arr){
                $search_goods_id_arr = [-1];
            }
        }
        $where=[
            ['status', '=', 1]
        ];
        if ($search_goods_id_arr){
            $where[]=['goods_id', 'in', $search_goods_id_arr];
        }
        if (isset($get['show_slogan']) && $get['show_slogan']){
            $where[]=['show_slogan', 'like', '%'.$get['show_slogan'].'%'];
        }
        $goods_all_list_arr = Show::where($where)
            ->page($get['page_no'], $get['page_size'])
            ->order('slogan_time desc')
            ->column('DISTINCT goods_id, slogan_time');
        $count = Show::where($where)
            ->value('count(DISTINCT goods_id, slogan_time) as count');
        if ($goods_all_list_arr){
            $goods_id_arr = array_column($goods_all_list_arr, 'goods_id');
            $goods_id_list = Goods::where([['status','=', 1], ['del', '=', 0], ['id', 'in', $goods_id_arr]])->column('id, name, poster as image2, image as image1, code', 'id');
            foreach ($goods_id_list as &$value){
                $value['image'] = $value['image2'] ? UrlServer::getFileUrl($value['image2']) : UrlServer::getFileUrl($value['image1']);
            }
        }
        $new_list=[];
        if($goods_all_list_arr){
            foreach ($goods_all_list_arr as $k=>$val){
                $time_map = strtotime($val['slogan_time']);
                $slogan_time = date('Y-m', $time_map);
                $new_list[$slogan_time]['slogan_time'] = $slogan_time;
                $new_list[$slogan_time]['slogan_year'] = date('Y', $time_map);
                $new_list[$slogan_time]['slogan_month'] = date('m', $time_map);
                $val['name'] = $goods_id_list[$val['goods_id']]['name'] ?? '';
                $val['image'] = $goods_id_list[$val['goods_id']]['image'] ?? '';
                $val['code'] = $goods_id_list[$val['goods_id']]['code'] ?? '';
                $new_list[$slogan_time]['data'][] = $val;
            }
        }
        $more = is_more($count, $get['page_no'], $get['page_size']);
        $data = [
            'lists'         => array_values($new_list),
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;

    }
    /**
     * Notes: 兼容 带有空格的查询
     * Author: Darren
     * DateTime: 2023-06-01 14:42
     */
    public static function showlists($user_id, $get){
        $where[] = ['status', '=', 1];//上架
        $keywords = $get['show_slogan'] ?? '';
        $whereOr=[];
        if ($keywords){
            $keywords = trim($keywords);
            $keywords = replaceNullTo($keywords);
            if (!strpos($keywords, ' ') === false){
                $keywords_arr = explode(' ', $keywords);
                if ($keywords_arr){
                    foreach ($keywords_arr as $item){
                        $whereOr[] = ['show_slogan|name','like','%'.$item.'%'];
                    }
                }
            }else{
                $where[] = ['show_slogan|name','like','%'.$keywords.'%'];
            }
        }
        if (isset($get['goods_id']) && $get['goods_id']){
            $where[] = ['goods_id','=',$get['goods_id']];
        }
        $lists = Show::where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->page($get['page_no'], $get['page_size'])
            ->order('slogan_time desc')
            ->column('id,image,show_slogan,slogan_time,goods_id');
        $count = Show::where($where)
            ->where(function ($query) use ($whereOr) {
                if ($whereOr){
                    $query->whereOr($whereOr);
                }
            })
            ->count();
        $goods_id_arr = array_column($lists, 'goods_id');
        $goods_list = Goods::where([['id', 'in', $goods_id_arr]])->column('id,name,code', 'id');
        $goods_show_arr_temp = GoodsShow::where([['goods_id', 'in', $goods_id_arr]])->column('id,goods_id,uri,sid');
        $goods_show_arr = [];
        foreach ($goods_show_arr_temp as $value){
            $value['uri'] = UrlServer::getFileUrl($value['uri']);
            $goods_show_arr[$value['sid']][] = $value['uri'];
        }
        if($lists) {
            foreach ($lists as $id=>$goods){
                $lists[$id]['goods_name'] = $goods_list[$goods['goods_id']]['name'];
                $lists[$id]['code'] = $goods_list[$goods['goods_id']]['code'];
                $lists[$id]['image'] = $lists[$id]['image'] ? UrlServer::getFileUrl($lists[$id]['image']) : '';
                $lists[$id]['show_list'] = array_values($goods_show_arr[$goods['id']]);
            }
            $lists = array_values($lists);
        }else{
            $lists = [];
        }
        //按照商品分组
        $goods_group_show_list =[];
        foreach ($lists as $val){
            $goods_group_show_list[$val['goods_id']][] = $val;
        }

        $new_list=[];
        if($lists){
            foreach ($lists as $k=>$val){
                $time_map = strtotime($val['slogan_time']);
                $slogan_time = date('Y-m', $time_map);
                $new_list[$slogan_time]['slogan_time'] = $slogan_time;
                $new_list[$slogan_time]['slogan_year'] = date('Y', $time_map);
                $new_list[$slogan_time]['slogan_month'] = date('m', $time_map);
                $new_list[$slogan_time]['slogan_day'] = date('d', $time_map);
                $new_list[$slogan_time]['data'][] = $val;
            }
        }
        $more = is_more($count, $get['page_no'], $get['page_size']);
        $data = [
            'lists'         => array_values($new_list),
            'page_no'       => $get['page_no'],
            'page_size'     => $get['page_size'],
            'count'         => $count,
            'more'          => $more
        ];
        return $data;


    }

    /**
     * Notes: 分享详情页
     * Author: Darren
     * DateTime: 2023-06-01 14:42
     */
    public static function showInfo($distribution_user_id, $get){
        $where_show=[
            'status'=>1,
            'goods_id'=>$get['id']
        ];
        $show_list = Show::where($where_show)->column('id,show_slogan,slogan_time,goods_id,create_time', 'id');
        if (!$show_list){
            return [];
        }
        $image = Goods::where('id',$get['id'])->value('image');
        $image = UrlServer::getFileUrl($image);
        $show_id_arr = array_column($show_list, 'id');
        $show_uri_list_temp = GoodsShow::where([['sid', 'in', $show_id_arr]])->column('uri,sid');
        foreach ($show_uri_list_temp as $value){
            $show_uri_list[$value['sid']][] = UrlServer::getFileUrl($value['uri']);
        }
        foreach ($show_list as $k=>&$item){
            $url_data=[
                'id'=>$get['id'],
                'type'=>1,
                'page_title'=>$item['show_slogan'],
                'distributionId'=>$get['distributionId'] ?? 0,
                'invite_code'=>$get['invite_code']
            ];
            $item['url'] = ShareLogic::getWechatMiniprogramUrl($distribution_user_id, $url_data);
            $item['goods_show'] = $show_uri_list[$k];
            $item['image'] = $image;
        }
        return array_values($show_list);
    }

    public static function shopRecommendGoodsList($shop_id, $limit=9){
        $cache_key = 'shop_recommend_goods_list';
        $cache_uri = $cache_key.'_'.$shop_id.'_'.$limit;
        $data_list = Cache::get($cache_uri);
        if ($data_list){
            return $data_list;
        }
        $onSaleWhere = [
            'del' => GoodsEnum::DEL_NORMAL, // 未删除
            'status' => GoodsEnum::STATUS_SHELVES, // 上架中
            'audit_status' => GoodsEnum::AUDIT_STATUS_OK, // 审核通过
        ];
        $data_list = Goods::field('id,name,image,market_price,min_price')
            ->where($onSaleWhere)
            ->where([
                'shop_id' => $shop_id,
                'is_recommend' => 1, // 推荐
            ])
            ->order([
                'sales_actual' => 'desc',
                'id' => 'desc'
            ])
            ->limit($limit)
            ->select()
            ->toArray();
        Cache::set($cache_uri, $data_list, Config::get('project.expire_time.'.$cache_key, 60));
        return $data_list;
    }

    /**
     * Notes: 获取商品所在店铺信息
     * Author: Darren
     * DateTime: 2023-06-17 17:00
     */
    public static function getGoodsShopInfo($goodsDetailArr){
        //店铺信息
        switch ($goodsDetailArr['shop']['type']){
            case 1 :
                $type_desc = '入驻商家';
                break;
            case 2 :
                $type_desc = '品牌专营商家';
                break;
            default :
                $type_desc = '品牌专营商家';
                break;
        }
        $follow = Db::name('shop_follow')->where(['shop_id' => $goodsDetailArr['shop_id'],'status' => 1])->count('id');
        $goodsDetailArr['shop']['type_desc'] = $type_desc; //商家类型
        $goodsDetailArr['shop']['follow_num'] = $follow; //收藏人数

        //客服二维码
        $customer_image = ConfigServer::get('shop_customer_service','image','',$goodsDetailArr['shop_id']);
        if($customer_image){
            $customer_image = UrlServer::getFileUrl($customer_image);
        }
        $goodsDetailArr['shop']['customer_image'] = $customer_image;
        $goodsDetailArr['shop']['goods_kefu_url'] = ConfigServer::get('shop_customer_service','goods_kefu_url', '', $goodsDetailArr['shop_id']);
        $goodsDetailArr['shop']['kefu_type'] = ConfigServer::get('shop_customer_service','type', '', $goodsDetailArr['shop_id']);
        $goodsDetailArr['shop']['work_wechat_company_id'] = ConfigServer::get('shop_customer_service','work_wechat_company_id', '', $goodsDetailArr['shop_id']);
        $onSaleWhere = [
            'del' => GoodsEnum::DEL_NORMAL, // 未删除
            'status' => GoodsEnum::STATUS_SHELVES, // 上架中
            'audit_status' => GoodsEnum::AUDIT_STATUS_OK, // 审核通过
        ];
        // 店铺在售商品数量
        $goodsDetailArr['shop']['goods_on_sale'] = Goods::where($onSaleWhere)
            ->where('shop_id', $goodsDetailArr['shop_id'])
            ->count();
        // 店铺推荐商品列表(9个)
        $goodsDetailArr['shop']['goods_list'] = [];// self::shopRecommendGoodsList($goodsDetailArr['shop_id'], 9);

        return $goodsDetailArr;
    }

    /**
     * Notes: 商品添加标签详情
     * Author: Darren
     * DateTime: 2023-07-01 17:30
     */
    public static function goodsDetailTags($tags){
        if (!$tags){
            return [];
        }
        if (is_string($tags)){
            $tags_arr = array_values(json_decode($tags, true));
        }elseif(is_array($tags)){
            $tags_arr = array_values($tags);
        }else{
            return [];
        }
        if (!$tags_arr){
            return [];
        }
        $list = GoodsTags::field('id,cat_code,name,remark,short_name,arc_detail_id')->where([['id', 'in', $tags_arr], ['del', '=', 0]])->order('sort asc')->select()->toArray();
        return array_values($list);
    }

    /**
     * Notes: 格式化商品属性
     * Author: Darren
     * DateTime: 2023-07-03 11:37
     */
    public static function goodsAttrFormat($goods_attr){
        if (!$goods_attr){
            return [];
        }
        if (is_string($goods_attr)){
            $attr_arr = json_decode($goods_attr, true);
        }else{
            return [];
        }
        if (!$attr_arr){
            return [];
        }
        $result=[];
        foreach ($attr_arr as $key=>$value){
            $result[]=[
                'key'=>$key,
                'value'=>$value
            ];
        }
        return $result;
    }

    public static function getCommunityArticle($goods_id){
        $field = 'ag.id, ag.article_id,ag.goods_id,ca.content,ca.image,ca.file_type,ca.status';
        $list = CommunityArticleGoods::alias('ag')
            ->field($field)
            ->leftjoin('community_article ca', 'ca.id = ag.article_id')
            ->where('ag.goods_id','=', $goods_id)
//            ->where('ca.file_type', '=', 10)
            ->where('ca.status', '=', 1)
            ->where('ca.del', '=', 0)
            ->order('ag.id asc')
            ->limit(1)
            ->select()
            ->toArray();
        $row =[];
        if ($list && isset($list[0]['article_id']) && $list[0]['article_id']){
            $row = $list[0];
            if($list[0]['file_type'] == '10'){
                $article_image = CommunityArticleImage::where('article_id', '=', $list[0]['article_id'])
                    ->limit(4)
                    ->column('image');
                if ($article_image){
                    foreach ($article_image as &$value){
                        $value = UrlServer::getFileUrl($value);
                    }
                    $row['article_image'] = $article_image;
                }
            }else if ($list[0]['file_type'] == '20'){
                $list1 = CommunityArticleGoods::alias('ag')
                    ->field($field)
                    ->leftjoin('community_article ca', 'ca.id = ag.article_id')
                    ->where('ag.goods_id','=', $goods_id)
                    ->where('ca.file_type', '=', 20)
                    ->where('ca.status', '=', 1)
                    ->where('ca.del', '=', 0)
                    ->order('ag.id asc')
                    ->limit(3)
                    ->select()
                    ->toArray();
                // 提取所有article_id组成数组
                $articleIds = array_column($list1, 'article_id');
                // 查询这些文章对应的所有图片（不限数量）
                $article_image = CommunityArticleImage::whereIn('article_id', $articleIds)
                    ->limit(3)
                    ->column('image');
//                $article_image = CommunityArticleImage::where('article_id', '=', $list[0]['article_id'])
//                    ->limit(3)
//                    ->column('image');
                if ($article_image){
                    $article_image_info = [];
                    foreach ($article_image as &$value){
                        array_push($article_image_info, ['article_image' => videoThumbnail(UrlServer::getFileUrl($value)),'article_video' => UrlServer::getFileUrl($value)]);

                    }
                    $row['article_image'] = $article_image_info;

//                    $row['article_video'] = UrlServer::getFileUrl($article_image[0]);
//                    $article_image = videoThumbnail(UrlServer::getFileUrl($article_image[0]));
//                    $row['article_image'] = $article_image;
                }
            }
        }
        return $row;
    }
}
