<?php

namespace app\common\server;

use app\common\enum\ClientEnum;
use app\common\enum\PayEnum;
use app\common\model\Client_;
use app\common\model\user\User;
use app\common\model\user\UserAuth;
use BsPaySdk\config\MerConfig;
use BsPaySdk\core\BsPay;
use BsPaySdk\core\BsPayClient;
use BsPaySdk\request\V2TradePaymentJspayRequest;
use BsPaySdk\request\V3TradePaymentJspayRequest;
use think\App;
use think\Exception;
use think\facade\Log;

require_once __DIR__ . '/../../../vendor/huifurepo/dg-php-sdk/BsPaySdk/init.php';
require_once __DIR__ . '/../../../vendor/autoload.php';

/**
 * 微信服务 服务类
 * Class WeChatServer
 * @package app\common\server
 */
class HuifuServer
{
    // SDK初始化标识
    private static $initialized = false;
    private static $request;
    private static $merConfig;

    public static $user_id;
    public static $userInfo;
    public static $client;
    public static $userAuth;
    public static $wx_data;


    private static function init()
    {
        if (!self::$initialized) {
            $file = app()->getRootPath() . '/config/huifu_config.json';
            self::$merConfig =  json_decode(file_get_contents($file), true);
            BsPay::init($file, false, 'default');
            self::$initialized = true;
        }
    }

    private static function initUser()
    {
        self::$userInfo = User::findOrEmpty(self::$user_id)->toArray();

        switch (self::$client ?? '') {
            case ClientEnum::mnp:
            case ClientEnum::h5:
            case ClientEnum::ios:
            case ClientEnum::android:
                self::$userAuth = UserAuth::where('user_id', self::$user_id)
                    ->where('client', self::$client)
                    ->order('id desc')
                    ->findOrEmpty()->toArray();
                $wechat_config  = WeChatServer::getMnpConfig();
                break;
            case ClientEnum::oa:
            case ClientEnum::pc:
            self::$userAuth = UserAuth::where('user_id', self::$user_id)
                    ->where('client', self::$client)
                    ->order('id desc')
                    ->findOrEmpty()->toArray();
                $wechat_config  = WeChatServer::getOaConfig();
                break;
            default:
                break;
        }
        self::$wx_data = [
//            'appid' => $wechat_config['app_id'] ?? '', // 公众号appid
//            'openid' => $wechat_config['openid'] ?? '', // 用户openid
            'sub_appid' => $wechat_config['app_id'] ?? '',//	子商户应用ID
            'sub_openid' => $wechat_config['openid'] ?? '', //子商户openid
            'body'=>'测试商品', // 商品描述
        ];
    }

    public static function unifiedOrder($from, $order, $order_source)
    {
        self::init();
        self::$user_id = $order['user_id'];
        self::initUser();
        self::$request = new V2TradePaymentJspayRequest();
        // 请求流水号
        self::$request->setReqSeqId($order['order_sn']);
        // 请求日期
        self::$request->setReqDate(date("Ymd"));
        // 商户号
        self::$request->setHuifuId(self::$merConfig['huifu_id']);
        // 交易金额
        self::$request->setTransAmt($order['order_sn']);
        // 商品描述
        self::$request->setGoodsDesc('测试商品');
        // 交易类型
        self::$request->setTradeType('T_MINIAPP');
        $extendInfo = [
//            'time_expire' => date('YmdHis', time()),
//            'delay_acct_flag'=> 'Y',
            'remark'=>implode('-', [
                $order['pay_way'],
                $order['client'],
                $order['from'],
                $order['id'],
            ]),
            'notify_url'=> (string) url('pay/hfdgPayWechatNotify', [], false, true),
            'wx_data' => self::$wx_data,
        ];
        self::$request->setExtendInfo($extendInfo);
        // 发起请求
        $result = self::getResult();
        return $result;
    }

    public static function getResult(){
        $client = new BsPayClient('default');
        $result = $client->postRequest(self::$request);
        //解析结果并且记录日志
        return $result;
    }


}